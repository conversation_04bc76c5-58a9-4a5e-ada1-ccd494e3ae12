<!-- 复制弹框 -->
<script setup>
import { ref, watch, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 复制弹框
const copyPopup = ref()

// 工序信息
const processInfo = ref()

// 输入框型体编号
const modelInput = ref('')
// 聚焦型体输入框
const focusModelInput = ref(false)
// 型体编号列表
const modelList = ref([])
// 可选型体编号列表
const modelOptionList = ref([])
// 已选型体编号列表
const selectedModelList = ref([])
// 是否复制图片
const isCopyPicture = ref(false)

// 工序信息列表
const processInfoList = inject('processInfoList')
// 搜索型体
const searchModel = inject('searchModel')
// 是否只显示自己创建的工序列表
const isMine = inject('isMine')
// 楦头编号
const lastNos = inject('lastNos')
// Outsole
const osNo = inject('osNo')
// 获取工序信息列表
const getProcessInfoList = inject('getProcessInfoList')
// 是否选择所有工序信息
const isSelectedAllProcessInfo = inject('isSelectedAllProcessInfo')

// 获取所有型体列表
async function getAllModelList() {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/getAllModelList',
    method: 'GET'
  }).then(res => {
    if (res.data.code) {
      modelList.value = res.data.data ? res.data.data : []
      modelOptionList.value = modelList.value.slice(0, 50)
    } else {
      modelList.value = []
      modelOptionList.value = []
      tipPopup.value.showTipPopup('warn', '暂无型体列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 显示复制弹框
async function showCopyPopup(param) {
  processInfo.value = param
  
  modelInput.value = ''
  selectedModelList.value = []
  
  if (modelList.value.length === 0) {
    await getAllModelList()
  }
  
  copyPopup.value.open()
}

// 选择型体
function selectModel(param) {
  if (!selectedModelList.value.includes(param)) {
    selectedModelList.value.push(param)
  }
}

// 删除型体
function deleteModel(param) {
  selectedModelList.value = selectedModelList.value.filter(item => item !== param)
}

// 复制工序
async function copyProcess() {
  if (selectedModelList.value.length === 0) {
    tipPopup.value.showTipPopup('warn', '请选择目标型体！')
    return
  }
  
  uni.showLoading({
    title: '复制中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/copyProcess',
    method: 'POST',
    data: {
      processInfo: JSON.stringify({
        ...processInfo.value,
        insUser: user,
        updUser: user
      }),
      isCopyPicture: isCopyPicture.value,
      targetModelList: selectedModelList.value
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getProcessInfoList(processInfo.value.operation, processInfo.value.brand)
      tipPopup.value.showTipPopup('success', '复制成功！')
      copyPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '复制失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

let modelInputTimer = null
watch(modelInput, () => {
  if (modelInputTimer) {
    clearTimeout(modelInputTimer)
  }
  modelInputTimer = setTimeout(() => {
    modelOptionList.value = modelList.value.filter(item => item.includes(modelInput.value.toUpperCase())).slice(0, 50)
  }, 300)
})

defineExpose({
  showCopyPopup
})
</script>

<template>
  <uni-popup
    ref="copyPopup"
    type="center"
    :is-mask-click="false"
    class="copy-popup"
  >
    <view class="container">
      <view class="top-bar flex-row-between-center">
        <view
          @click="copyPopup.close()"
          class="cancel button"
        >
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <view class="title">
          请选择目标型体
        </view>
        
        <view
          @click="copyProcess()"
          class="submit button"
        >
          <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
        </view>
      </view>
      
      <view class="description flex-row-center">
        {{ operationMap.get(processInfo.operation) ? operationMap.get(processInfo.operation) : '未知' }} - {{ proSeqMap.get(processInfo.proSeq) ? proSeqMap.get(processInfo.proSeq) : '未知' }} - {{ processInfo.model }} - {{ processInfo.rtgCode }}
      </view>
      
      <view class="copy-picture">
        <text>不复制图片</text>
        
        <view @click="isCopyPicture = !isCopyPicture" class="switch">
          <view class="inner" :style="{ transform: isCopyPicture ? 'translateX(0px)' : 'translateX(-40px)' }">
            <view class="ball"></view>
          </view>
        </view>
        
        <text>复制图片</text>
      </view>
      
      <view class="search">
        <input
          v-model="modelInput"
          @focus="focusModelInput = true"
          @blur="focusModelInput = false"
          type="text"
          placeholder="请输入型体编号"
          class="model-input input"
          :style="{
            boxShadow: focusModelInput ? '0 0 5px blue' : '0 0 5px gray'
          }"
        />
        
        <view v-show="modelInput" @click="modelInput = ''" class="model-clear button">
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <!-- #ifdef APP -->
        <uni-transition
          :show="focusModelInput"
          mode-class="fade"
          class="model-option"
        >
        <!-- #endif -->
        <!-- #ifdef WEB -->
        <transition name="fade">
        <view v-show="focusModelInput" class="model-option">
        <!-- #endif -->
          <view
            v-for="item in modelOptionList"
            @click="selectModel(item)"
            class="model-option-item"
          >
            {{ item }}
          </view>
          
          <view v-show="modelOptionList.length === 0" class="model-option-empty">
            暂无可选型体
          </view>
        <!-- #ifdef WEB -->
        </view>
        </transition>
        <!-- #endif -->
        <!-- #ifdef APP -->
        </uni-transition>
        <!-- #endif -->
      </view>
      
      <view class="select flex-row-start-center">
        <view v-for="item in selectedModelList" class="selected-model">
          <view class="button">
            {{ item }}
          </view>
          
          <view @click="deleteModel(item)" class="delete-model">
            <uni-icons type="closeempty" size="20" color="darkred"></uni-icons>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.copy-popup {
  .container {
    width: 500px;
    height: 600px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .top-bar {
      width: 100%;
      height: 70px;
      padding: 0 10px;
      
      .title {
        font-size: 22px;
        font-weight: bold;
      }
      
      .cancel, .submit {
        width: 75px;
        height: 50px;
      }
    }
    
    .description {
      width: 100%;
      height: 40px;
      font-size: 20px;
      font-weight: bold;
    }
    
    .copy-picture {
      width: 100%;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
      
      .switch {
        width: 80px;
        height: 40px;
        margin: 10px;
        background-color: #f6f6f6;
        border-radius: 20px;
        box-shadow: 0 0 5px gray;
        overflow: hidden;
        /* #ifdef WEB */
        cursor: pointer;
        /* #endif */
        
        .inner {
          width: 80px;
          height: 40px;
          background-color: skyblue;
          border-radius: 20px;
          transition: all 0.25s ease;
          
          .ball {
            width: 40px;
            height: 40px;
            margin-left: 40px;
            background-color: white;
            border-radius: 20px;
            box-shadow: 0 0 5px gray;
          }
        }
      }
    }
    
    .search {
      width: 100%;
      height: 70px;
      padding: 10px 50px;
      position: relative;
      
      .model-input {
        width: 100%;
        height: 100%;
        padding: 0 10px;
      }
      
      .model-clear {
        width: 60px;
        height: 40px;
        margin-top: 5px;
        position: absolute;
        top: 10px;
        right: 55px;
      }
      
      .model-option {
        width: calc(100% - 100px);
        max-height: 250px;
        position: absolute;
        top: 100%;
        left: 50px;
        border-radius: 10px;
        box-shadow: 0 0 5px gray;
        background-color: #fdf6e3;
        z-index: 2;
        overflow: auto;
        
        .model-option-item, .model-option-empty {
          width: 100%;
          min-height: 50px;
          padding: 5px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 20px;
          font-weight: bold;
          text-align: center;
          border-top: 1px solid #ddd;
        }
        
        .model-option-item {
          transition: all 0.1s linear;
          
          /* #ifdef APP */
          &:active {
            background-color: #ccc;
          }
          /* #endif */
          
          /* #ifdef WEB */
          cursor: pointer;
          &:hover {
            background-color: #ccc;
          }
          /* #endif */
        }
        
        .model-option-item:first-child, .model-option-empty {
          border: none;
        }
      }
    }
    
    .select {
      width: 100%;
      max-height: 420px;
      padding: 5px;
      overflow: auto;
      
      .selected-model {
        width: calc(50% - 30px);
        height: 40px;
        margin: 15px;
        position: relative;
        
        .button {
          height: 100%;
          width: 100%;
        }
        
        .delete-model {
          width: 30px;
          height: 30px;
          position: absolute;
          right: -15px;
          top: -15px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: bold;
          text-align: center;
          background-color: #fdf6e3;
          border-radius: 50%;
          box-shadow: 0 0 5px gray;
          transition: all 0.1s linear;
          z-index: 1;
          /* #ifdef WEB */
          cursor: pointer;
          /* #endif */
          
          &:active {
            transform: scale(0.98);
            box-shadow: 0 0 1px gray;
            /* #ifdef APP */
            background-color: #ccc;
            /* #endif */
          }
          
          /* #ifdef WEB */
          &:hover {
            background-color: #ccc;
          }
          /* #endif */
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
  
  .flex-row-start-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
  }
  
  .flex-row-between-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }
  
  /* #ifdef WEB */
  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.3s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  
  .fade-enter-to,
  .fade-leave-from {
    opacity: 1;
  }
  /* #endif */
}
</style>