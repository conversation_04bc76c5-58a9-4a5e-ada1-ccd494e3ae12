package com.zqn.modeldata2.entity.sop;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * SOP 工序信息
 */
@Data
public class SopProcessInfo {
    // 客户
    private String brand;
    // 型体编号
    private String model;
    // 型体描述
    private String modelDesc;
    // 楦头编号
    private String lastNos;
    // Outsole
    private String osNo;
    // 制程
    private String operation;
    // 详细制程
    private String proSeq;
    // 主要代码
    private String rtgCode;
    // 生产类型
    private String rtgType;
    // 材质
    private String material;
    // 完成状态
    private String cplFlag;
    // 完成时间
    private Date cplDate;
    // 流程数
    private int flowNumber;
    // 创建方式
    private String createType;
    // 成品编号
    private String proNos;
    // SKU 列表
    private List<SopSku> skuList;
    // 添加者
    private String insUser;
    // 添加时间
    private Date insDate;
    // 添加者姓名
    private String insName;
    // 修改者
    private String updUser;
    // 修改时间
    private Date updDate;
    // 页数
    private int pageNo;
    // 页大小
    private int pageSize;
}
