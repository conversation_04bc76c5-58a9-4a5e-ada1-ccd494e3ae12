package com.zqn.modeldata2.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.entity.*;
import com.zqn.modeldata2.mapper.MattingMapper;
import com.zqn.modeldata2.service.MattingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MattingServiceImpl implements MattingService {


    @Resource
    private MattingMapper mattingMapper;

    private static final Logger LOG = LoggerFactory.getLogger(MattingServiceImpl.class);


    private String getFirstDay(String inputDate) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM");
            Date date = dateFormat.parse(inputDate);

            // 获取Calendar实例并设置为给定日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);

            // 设置为当月第一天
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            String firstDayOfMonth = dateFormat.format(calendar.getTime());

            // 由于SimpleDateFormat格式化后的日期没有日，需要手动添加日
            SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
            String day = dayFormat.format(calendar.getTime());

            return firstDayOfMonth + "/" + day;
        } catch (ParseException e) {
            e.printStackTrace();
            throw new RuntimeException("格式化时间错误");
        }
    }

    private String getEndDay(String inputDate) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM");
            Calendar calendar = Calendar.getInstance();
            Date date = dateFormat.parse(inputDate);
            calendar.setTime(date);

            // 获取该月最后一天
            int lastDayOfMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            calendar.set(Calendar.DAY_OF_MONTH, lastDayOfMonth);
            return dateFormat.format(calendar.getTime()) + "/" + lastDayOfMonth;
        } catch (ParseException e) {
            e.printStackTrace();
            throw new RuntimeException("格式化时间错误");
        }
    }

    @Override
    public List<BottomOrder> bottomList(String startTime, String endTime) {
        List<BottomOrder> bottomOrders = mattingMapper.bottomList(startTime, endTime);
        bottomOrders =  bottomOrders.stream().map(item -> {
            if(item.getMODEL_PIC() != null){
                String base64 = Base64.getEncoder().encodeToString(item.getMODEL_PIC());
                item.setPic_base64("data:image/png;base64," + base64);
//                item.setMODEL_PIC(null);
            }
            return  item;
        }).collect(Collectors.toList());
        return bottomOrders;
    }

    @Override
    public List<BottomOrderItem> getBottomItem(String modelNo, String mline) {
        return  mattingMapper.getBottomItem(modelNo, mline);
    }




    @Override
    @Transactional
    public List<Matting> callMatting(String startDate) {
        if (startDate == null || startDate.isEmpty()) {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
            startDate = dateFormat.format(calendar.getTime());
        }
        try {
            mattingMapper.callPdMkLastcomp(getFirstDay(startDate), getEndDay(startDate));
            List<Matting> result = mattingMapper.selectMkTpMattingGroupBy();
            List<Matting> resultNew = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(result)) {
                Map<String, List<Matting>> map = result.stream()
                        .collect(Collectors.groupingBy(Matting::getPd_line));
                resultNew.addAll(map.get("成型線"));
                resultNew.addAll(map.get("PCC成型A"));
                for (Map.Entry<String, List<Matting>> entry : map.entrySet()) {
                    if (!entry.getKey().equals("成型線") && !entry.getKey().equals("PCC成型A")) {
                        resultNew.addAll(entry.getValue());
                    }
                }
            }
            return resultNew;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOG.error("手动结案 功能报错！{}", e.getMessage());
//                throw new RuntimeException(result);
        }

        return null;
    }

    @Override
    public List<Fit> query(String factory, String brandNo, String startTime, String endTime, String cfmFlag, String clFlag) {
//        PageHelper.startPage(pageNo, pageSize);
        List<Fit> list = mattingMapper.query(factory, brandNo, startTime, endTime, cfmFlag, clFlag);
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> modelNoList = list.stream().map(Fit::getModel_no).distinct().collect(Collectors.toList());
            List<Image> images = mattingMapper.queryImages(modelNoList);
            if(CollectionUtil.isNotEmpty(images)){
                Map<String, byte[]> imageMap = images.stream()
                        .collect(Collectors.toMap(Image::getModel_no, Image::getModel_pic));
                list.stream().forEach(item -> {
                    byte[] bytes = imageMap.get(item.getModel_no());
                    if(bytes != null){
                        item.setModel_pic(bytes);
                        String base64 = Base64.getEncoder().encodeToString(bytes);
                        item.setPic_base64("data:image/png;base64," + base64);
                    }
                });
            }
        }
        return list;
//        return new PageInfo<>(list);
    }

    @Override
    public List<Fit> queryList(String factory, String brandNo, String startTime, String endTime, String cfmFlag, String clFlag) {
        List<Fit> list = mattingMapper.queryList(factory, brandNo, startTime, endTime, cfmFlag, clFlag);
        if (CollectionUtil.isNotEmpty(list)) {
            list = list.stream().map(item -> {
                if (item.getModel_pic() != null) {
                    byte[] picture = item.getModel_pic();
                    String base64 = Base64.getEncoder().encodeToString(picture);
                    item.setPic_base64("data:image/png;base64," + base64);
                }
                if (StrUtil.isNotBlank(item.getT1_flag()) && "y".equalsIgnoreCase(item.getT1_flag())) {
                    item.setT1_flag("ok");
                } else {
                    item.setT1_flag("");
                }
                if (StrUtil.isNotBlank(item.getT2_flag()) && "y".equalsIgnoreCase(item.getT2_flag())) {
                    item.setT2_flag("ok");
                } else {
                    item.setT2_flag("");
                }
                if (StrUtil.isNotBlank(item.getT3_flag()) && "y".equalsIgnoreCase(item.getT3_flag())) {
                    item.setT3_flag("ok");
                } else {
                    item.setT3_flag("");
                }
                if (StrUtil.isNotBlank(item.getY_flag()) && "y".equalsIgnoreCase(item.getY_flag())) {
                    item.setY_flag("ok");
                } else {
                    item.setY_flag("");
                }
                return item;
            }).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<DaYuan> queryDayuanList(String brandNo,  String startTime, String endTime) {
        List<DaYuan> list = mattingMapper.queryDayuanList(brandNo,startTime, endTime );
        list.stream().forEach(item -> {
            if(item.getModel_pic() != null){
                String base64 = Base64.getEncoder().encodeToString(item.getModel_pic());
                item.setPic_base64("data:image/png;base64," + base64);
                item.setModel_pic(null);
            }
        });
        return list;
    }

    @Override
    public List<Brand> queryDayuanBrands() {
        List<Brand> list = mattingMapper.queryDayuanBrands();
        return list;
    }


}
