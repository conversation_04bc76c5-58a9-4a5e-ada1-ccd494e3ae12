package com.zqn.modeldata2.controller.export;

import cn.afterturn.easypoi.cache.ExcelCache;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.zqn.modeldata2.entity.sop.SopExcelExport;
import com.zqn.modeldata2.entity.sop.SopFlowPicture;
import com.zqn.modeldata2.entity.sop.SopPress;
import com.zqn.modeldata2.service.SopService;
import com.zqn.sop.util.ExcelExportUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

@Controller
@RequestMapping("/sop/ua/ymexport")
public class SopUAYmExportController {

    @Autowired
    private SopService sopService;

    @Value("${uploadUrl}")
    private String TARGET_FOLDER;

    @GetMapping("/excel")
    public void exportOperationProcess(
            @RequestParam(value = "model") String model,
            @RequestParam(value = "operation") String operation,
            @RequestParam(value = "rtgCode") String rtgCode,
            HttpServletResponse response
    ) throws IOException {
        String filePath = "static/excel/ua/加工/压模.xlsx";
        Workbook tempWorkbook = ExcelCache.getWorkbook(filePath, new Integer[]{0, 1, 2, 3}, true);
        Map<Integer, List<Map<String, Object>>> resultMap = new TreeMap<>();
        List<String> sheetNameList = new ArrayList<>();

        Map<String, Map<String, String>> imageNameMap = new TreeMap<>();

        List<String> partList = sopService.getExistingPartList(model, operation, rtgCode);
        List<SopExcelExport> exportList = sopService.getExcelExportList(model, operation, rtgCode, "ALL");

        List<Map<String, Object>> mapList = new ArrayList<>();
        for (int partIndex = 0; partIndex < partList.size(); partIndex++) {
            List<SopPress> pressList = sopService.getPressList(model, operation, rtgCode, partList.get(partIndex));

            Map<String, Object> map = new TreeMap<>();
            Map<String, String> imageMap = new HashMap<>();

            if (!pressList.isEmpty()) {
                map.put("model", pressList.get(0).getModel_no());
                map.put("componentName", pressList.get(0).getComponent_name());
                map.put("vendor", pressList.get(0).getVendor());
                map.put("processCondition", pressList.get(0).getProcess_condition());
            }

            if (!exportList.isEmpty()) {
                map.put("modelDesc", exportList.get(0).getModelDesc());
            }

            for (SopPress sopPress : pressList) {
                if (sopPress.getPress_type() == 1) {
                    map.put("topThickness1", sopPress.getTop_thickness());
                    map.put("topHardness1", sopPress.getTop_hardness());
                    map.put("topColor1", sopPress.getTop_color());
                    map.put("topChangeFrequency1", sopPress.getTop_change_frequency());
                    map.put("topMcSetTemp1", sopPress.getTop_mc_set_temp());
                    map.put("topActualTemp1", sopPress.getTop_actual_temp());
                    map.put("bottomThickness1", sopPress.getBottom_thickness());
                    map.put("bottomHardness1", sopPress.getBottom_hardness());
                    map.put("bottomColor1", sopPress.getBottom_color());
                    map.put("bottomChangeFrequency1", sopPress.getBottom_change_frequency());
                    map.put("bottomMcSetTemp1", sopPress.getBottom_mc_set_temp());
                    map.put("bottomActualTemp1", sopPress.getBottom_actual_temp());
                    map.put("time1", sopPress.getTime());
                    map.put("probeLocationRemark1", sopPress.getProbe_location_remark());

                    for (int i = 1; i < 8; i++) {
                        try {
                            map.put("size1" + i, sopPress.getClass().getMethod("getSize" + i).invoke(sopPress));
                            map.put("mcSetting1" + i, sopPress.getClass().getMethod("getMc_setting" + i).invoke(sopPress));
                            map.put("loadCell1" + i, sopPress.getClass().getMethod("getLoad_cell" + i).invoke(sopPress));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    if (sopPress.getProbe_location_image() != null) {
                        imageMap.put("probeLocationImage1", sopPress.getProbe_location_image());
                    } else {
                        imageMap.put("probeLocationImage1", "");
                    }
                } else if (sopPress.getPress_type() == 2) {
                    map.put("topThickness2", sopPress.getTop_thickness());
                    map.put("topHardness2", sopPress.getTop_hardness());
                    map.put("topColor2", sopPress.getTop_color());
                    map.put("topChangeFrequency2", sopPress.getTop_change_frequency());
                    map.put("topMcSetTemp2", sopPress.getTop_mc_set_temp());
                    map.put("topActualTemp2", sopPress.getTop_actual_temp());
                    map.put("bottomThickness2", sopPress.getBottom_thickness());
                    map.put("bottomHardness2", sopPress.getBottom_hardness());
                    map.put("bottomColor2", sopPress.getBottom_color());
                    map.put("bottomChangeFrequency2", sopPress.getBottom_change_frequency());
                    map.put("bottomMcSetTemp2", sopPress.getBottom_mc_set_temp());
                    map.put("bottomActualTemp2", sopPress.getBottom_actual_temp());
                    map.put("time2", sopPress.getTime());
                    map.put("probeLocationRemark2", sopPress.getProbe_location_remark());

                    for (int i = 1; i < 8; i++) {
                        try {
                            map.put("size2" + i, sopPress.getClass().getMethod("getSize" + i).invoke(sopPress));
                            map.put("mcSetting2" + i, sopPress.getClass().getMethod("getMc_setting" + i).invoke(sopPress));
                            map.put("loadCell2" + i, sopPress.getClass().getMethod("getLoad_cell" + i).invoke(sopPress));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    if (sopPress.getProbe_location_image() != null) {
                        imageMap.put("probeLocationImage2", sopPress.getProbe_location_image());
                    } else {
                        imageMap.put("probeLocationImage2", "");
                    }
                }
            }
            mapList.add(map);
            if (partList.get(partIndex) != null) {
                sheetNameList.add("鞋面热冷压规格 " + partList.get(partIndex));
                imageNameMap.put("鞋面热冷压规格 " + partList.get(partIndex), imageMap);
            } else {
                sheetNameList.add("鞋面热冷压规格 未知部位");
                imageNameMap.put("鞋面热冷压规格 未知部位", imageMap);
            }
        }
        resultMap.put(0, mapList);

        // 鞋面部位摆放流程(表头)
        List<Map<String, Object>> mapList1 = new ArrayList<>();
        Map<String, Object> map1 = new TreeMap<>();
        if (!exportList.isEmpty()) {
            map1.put("modelDesc", exportList.get(0).getModelDesc());
        }
        map1.put("model", model);
        mapList1.add(map1);
        resultMap.put(1, mapList1);
        sheetNameList.add("鞋面部位摆放流程(表头)");

        System.out.println("######" + exportList);

        // 鞋面部位摆放流程(表中)
        List<Map<String, Object>> mapList2 = new ArrayList<>();
        for (int i = 0; i < exportList.size(); i += 4) {
            Map<String, Object> map2 = new TreeMap<>();
            map2.put("item", i / 4 + 1);
            for (int j = i; j < (i + 4) && j < exportList.size(); j++) {
                switch (j % 4) {
                    case 0:
                        map2.put("componentName1", exportList.get(j).getPartName());
                        map2.put("actions1", exportList.get(j).getActions());
                        map2.put("cycleTime1", exportList.get(j).getProcessOption2() != null ? exportList.get(j).getProcessOption2().trim() : "");
                        break;
                    case 1:
                        map2.put("componentName2", exportList.get(j).getPartName());
                        map2.put("actions2", exportList.get(j).getActions());
                        map2.put("cycleTime2", exportList.get(j).getProcessOption2() != null ? exportList.get(j).getProcessOption2().trim() : "");
                        break;
                    case 2:
                        map2.put("componentName3", exportList.get(j).getPartName());
                        map2.put("actions3", exportList.get(j).getActions());
                        map2.put("cycleTime3", exportList.get(j).getProcessOption2() != null ? exportList.get(j).getProcessOption2().trim() : "");
                        break;
                    case 3:
                        map2.put("componentName4", exportList.get(j).getPartName());
                        map2.put("actions4", exportList.get(j).getActions());
                        map2.put("cycleTime4", exportList.get(j).getProcessOption2() != null ? exportList.get(j).getProcessOption2().trim() : "");
                        break;
                    default:
                        break;
                }
            }
            mapList2.add(0, map2);
        }
        resultMap.put(2, mapList2);
        for (int i = 0; i < mapList2.size(); i++) {
            sheetNameList.add("鞋面部位摆放流程(表中) " + (i + 1));
        }

        // 鞋面部位摆放流程(表尾)
        List<Map<String, Object>> mapList3 = new ArrayList<>();
        Map<String, Object> map3 = new TreeMap<>();
        mapList3.add(map3);
        resultMap.put(3, mapList3);
        sheetNameList.add("鞋面部位摆放流程(表尾)");

        TemplateExportParams params = new TemplateExportParams(filePath, true);
        params.setTemplateWb(tempWorkbook);
        XSSFWorkbook workbook = (XSSFWorkbook) ExcelExportUtil.exportExcelClone(resultMap, params);

        for (int i = 0; i < sheetNameList.size(); i++) {
            settingPrintSetup(workbook.getSheetAt(i), (short) 100);
            workbook.setSheetName(i, sheetNameList.get(i));
        }

        // 鞋面热冷压规格图片
        for (Map.Entry<String, Map<String, String>> entry : imageNameMap.entrySet()) {
            String sheetName = entry.getKey();
            Map<String, String> imageMap = entry.getValue();

            if (imageMap.get("probeLocationImage1") != null) {
                String[] imageArray1 = imageMap.get("probeLocationImage1").split(",");
                if ((imageArray1.length == 1 && !imageArray1[0].isEmpty()) || imageArray1.length > 1) {
                    List<byte[]> imageList = new ArrayList<>();
                    for (String image : imageArray1) {
                        String imagePath = TARGET_FOLDER + image;
                        byte[] compressedImage = compressImage(imagePath);
                        imageList.add(compressedImage);
                    }
                    XSSFSheet sheet = workbook.getSheet(sheetName);
                    XSSFDrawing patriarch = sheet.createDrawingPatriarch();
                    insertImages(patriarch, workbook, imageList, 17, 46, 3, 3, 3);
                }
            }

            if (imageMap.get("probeLocationImage2") != null) {
                String[] imageArray2 = imageMap.get("probeLocationImage2").split(",");
                if ((imageArray2.length == 1 && !imageArray2[0].isEmpty()) || imageArray2.length > 1) {
                    List<byte[]> imageList = new ArrayList<>();
                    for (String image : imageArray2) {
                        String imagePath = TARGET_FOLDER + image;
                        byte[] compressedImage = compressImage(imagePath);
                        imageList.add(compressedImage);
                    }
                    XSSFSheet sheet = workbook.getSheet(sheetName);
                    XSSFDrawing patriarch = sheet.createDrawingPatriarch();
                    insertImages(patriarch, workbook, imageList, 39, 40, 3, 3, 4);
                }
            }
        }

        for (int i = 0; i < exportList.size(); i++) {
            SopExcelExport excelExport = exportList.get(i);
            List<SopFlowPicture> imageList = excelExport.getImgList();
            if (!CollectionUtils.isEmpty(imageList)) {
                List<byte[]> byteImageList = new ArrayList<>();

                for (SopFlowPicture sopFlowPicture : imageList) {
                    String picturePath = TARGET_FOLDER + sopFlowPicture.getImgUrl();
                    byte[] compressedImage = compressImage(picturePath);
                    byteImageList.add(compressedImage);
                }

                XSSFSheet sheet = workbook.getSheet("鞋面部位摆放流程(表中) " + (i / 4 + 1));
                XSSFDrawing patriarch = sheet.createDrawingPatriarch();
                switch (i % 4) {
                    case 0:
                        insertImagesInGrid(patriarch, workbook, byteImageList, 1, 6,  3, 3);
                        break;
                    case 1:
                        insertImagesInGrid(patriarch, workbook, byteImageList, 1, 19, 3, 3);
                        break;
                    case 2:
                        insertImagesInGrid(patriarch, workbook, byteImageList, 1, 32, 3, 3);
                        break;
                    case 3:
                        insertImagesInGrid(patriarch, workbook, byteImageList, 1, 45, 3, 3);
                        break;
                    default:
                        break;
                }
            }
        }

        // 合并开头工作表的下标
        int startIndex = workbook.getSheetIndex("鞋面部位摆放流程(表头)");
        // 合并结尾工作表的下标
        int endIndex = workbook.getSheetIndex("鞋面部位摆放流程(表尾)");
        // 合并工作表
        XSSFSheet mergedSheet = workbook.createSheet("鞋面部位摆放流程");

        int currentRowNum = 0;

        for (int i = startIndex; i <= endIndex; i++) {
            XSSFSheet sourceSheet = workbook.getSheetAt(i);

            // 设置列宽（仅第一次）
            if (currentRowNum == 0) {
                for (int colIdx = 0; colIdx < sourceSheet.getRow(0).getLastCellNum(); colIdx++) {
                    mergedSheet.setColumnWidth(colIdx, sourceSheet.getColumnWidth(colIdx));
                }
            }

            // 缓存行数据以避免并发修改
            List<Row> rows = new ArrayList<>();
            for (Row row : sourceSheet) {
                if (!isRowEmpty(row)) {
                    rows.add(row);
                }
            }

            // 复制行
            for (Row sourceRow : rows) {
                Row newRow = mergedSheet.createRow(currentRowNum++);
                copyRow(sourceRow, newRow, workbook);
            }

            int startRow = currentRowNum - rows.size();

            // 复制合并区域
            copyMergedRegions(sourceSheet, mergedSheet, startRow);

            // 复制图片
            copyPictures(sourceSheet, mergedSheet, startRow);
        }

        // 删除原始 sheet（从后往前删）
        for (int i = endIndex; i >= startIndex; i--) {
            workbook.removeSheetAt(i);
        }

        String fileName = model + "-压模-" + rtgCode + ".xlsx";

        response.reset();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*"); // 或者指定具体的域名
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        try {
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 压缩图片
    public static byte[] compressImage(String inputImagePath) {
        try {
            // 读取图像
            BufferedImage bufferImg = ImageIO.read(new File(inputImagePath));

            // 获取图片格式
            String formatName = inputImagePath.substring(inputImagePath.lastIndexOf(".") + 1);
            if (formatName.equals("jfif")) {
                formatName = "jpeg";
            }

            // 获取图片写入器
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(formatName);
            if (!writers.hasNext()) throw new IllegalStateException("No writers found");
            ImageWriter writer = writers.next();

            // 设置输出流
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            ImageOutputStream ios = ImageIO.createImageOutputStream(byteArrayOut);
            writer.setOutput(ios);

            // 设置压缩参数
            ImageWriteParam param = writer.getDefaultWriteParam();
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.2f); // 这里设置压缩质量
            }

            // 写入图像
            writer.write(null, new IIOImage(bufferImg, null, null), param);

            // 关闭流
            ios.close();
            writer.dispose();

            return byteArrayOut.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    // 插入图片
    private void insertImages(XSSFDrawing patriarch, XSSFWorkbook workbook, List<byte[]> imageBytesList, int startRow, int startCol, int rowHeight, int colWidth, int count) {
        for (int i = 0; i < imageBytesList.size(); i++) {
            int col1 = startCol + (i % count) * colWidth + (i % count);
            int row1 = startRow + (i / count) * rowHeight;
            int col2 = col1 + colWidth;
            int row2 = row1 + rowHeight;
            byte[] imageBytes = imageBytesList.get(i);
            XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, col1, row1, col2, row2);
            patriarch.createPicture(anchor, workbook.addPicture(imageBytes, XSSFWorkbook.PICTURE_TYPE_PNG));
        }
    }

    private void insertImagesInGrid(XSSFDrawing patriarch, XSSFWorkbook workbook, List<byte[]> imageBytesList, int startRow, int startCol, int rowHeight, int colWidth) {
        int numRows = 3; // 网格行数
        int numCols = 4; // 网格列数
        for (int i = 0; i < numRows; i++) {
            for (int j = 0; j < numCols; j++) {
                int index = i * numCols + j;
                if (index >= imageBytesList.size()) {
                    return; // 如果图片数量少于网格单元格数量，提前返回
                }
                byte[] imageBytes = imageBytesList.get(index);
                int row1 = startRow + i * rowHeight;
                int col1 = startCol + j * colWidth;
                int row2 = row1 + rowHeight;
                int col2 = col1 + colWidth;

                // 创建锚点
                XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) col1, row1, (short) col2, row2);
                patriarch.createPicture(anchor, workbook.addPicture(imageBytes, XSSFWorkbook.PICTURE_TYPE_PNG));
            }
        }
    }

    private void settingPrintSetup(Sheet targetSheet, short scale) {
        PrintSetup targetPrintSetup = targetSheet.getPrintSetup();
        targetPrintSetup.setPaperSize((short) 9);
        targetPrintSetup.setScale(scale);
        targetPrintSetup.setLandscape(true);
    }

    public static boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }

        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);

            // 检查单元格是否有内容
            if (cell.getCellType() != CellType.BLANK) {
                return false; // 单元格包含数据
            }

            // 获取单元格的边框属性
            CellStyle style = cell.getCellStyle();
            boolean hasBorder = style.getBorderTop() != BorderStyle.NONE ||
                                style.getBorderBottom() != BorderStyle.NONE ||
                                style.getBorderLeft() != BorderStyle.NONE ||
                                style.getBorderRight() != BorderStyle.NONE;

            if (hasBorder) {
                return false; // 单元格有边框，不算空行
            }
        }
        return true; // 所有单元格既没有数据也没有边框
    }

    public static void copyRow(Row sourceRow, Row targetRow, XSSFWorkbook workbook) {
        // 设置行高
        targetRow.setHeight(sourceRow.getHeight());

        // 设置单元格内容和样式
        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            Cell targetCell = targetRow.createCell(i);

            if (sourceCell != null) {
                // 复制单元格样式
                targetCell.setCellStyle(sourceCell.getCellStyle());

                // 复制单元格值
                switch (sourceCell.getCellType()) {
                    case STRING:
                        targetCell.setCellValue(sourceCell.getStringCellValue());
                        break;
                    case NUMERIC:
                        targetCell.setCellValue(sourceCell.getNumericCellValue());
                        break;
                    case BOOLEAN:
                        targetCell.setCellValue(sourceCell.getBooleanCellValue());
                        break;
                    case FORMULA:
                        targetCell.setCellFormula(sourceCell.getCellFormula());
                        break;
                    case BLANK:
                        targetCell.setBlank();
                        break;
                    case ERROR:
                        targetCell.setCellErrorValue(sourceCell.getErrorCellValue());
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private void copyMergedRegions(XSSFSheet sourceSheet, XSSFSheet targetSheet, int startRowIndex) {
        for (int i = 0; i < sourceSheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = sourceSheet.getMergedRegion(i);
            CellRangeAddress newMergedRegion = new CellRangeAddress(
                    mergedRegion.getFirstRow() + startRowIndex,
                    mergedRegion.getLastRow() + startRowIndex,
                    mergedRegion.getFirstColumn(),
                    mergedRegion.getLastColumn()
            );
            targetSheet.addMergedRegion(newMergedRegion);
        }
    }

    /**
     * 复制原始 sheet 中的所有图片到目标 sheet，并根据行号偏移锚点位置
     */
    public static void copyPictures(XSSFSheet sourceSheet, XSSFSheet targetSheet, int rowOffset) {
        // 获取源 sheet 的绘图容器
        XSSFDrawing sourceDrawing = (XSSFDrawing) sourceSheet.getDrawingPatriarch();
        if (sourceDrawing == null) return;

        // 创建或获取目标 sheet 的绘图容器
        XSSFDrawing targetDrawing = (XSSFDrawing) targetSheet.createDrawingPatriarch();

        // 遍历所有图形对象
        for (XSSFShape shape : sourceDrawing.getShapes()) {
            if (shape instanceof XSSFPicture) {
                XSSFPicture picture = (XSSFPicture) shape;
                XSSFClientAnchor sourceAnchor = (XSSFClientAnchor) picture.getAnchor();

                // 构建新的锚点并偏移行号
                XSSFClientAnchor newAnchor = new XSSFClientAnchor();
                newAnchor.setCol1(sourceAnchor.getCol1());
                newAnchor.setCol2(sourceAnchor.getCol2());
                newAnchor.setRow1(sourceAnchor.getRow1() + rowOffset);
                newAnchor.setRow2(sourceAnchor.getRow2() + rowOffset);

                // 获取图片数据和类型
                byte[] imageData = getPictureData(picture);
                int pictureType = picture.getPictureData().getPictureType();

                // 添加图片到目标工作簿
                XSSFWorkbook workbook = (XSSFWorkbook) targetSheet.getWorkbook();
                int pictureIdx = workbook.addPicture(imageData, pictureType);

                // 插入图片到目标 sheet
                targetDrawing.createPicture(newAnchor, pictureIdx);
            }
        }
    }

    /**
     * 提取图片字节流
     */
    private static byte[] getPictureData(XSSFPicture picture) {
        return picture.getPictureData().getData();
    }
}
