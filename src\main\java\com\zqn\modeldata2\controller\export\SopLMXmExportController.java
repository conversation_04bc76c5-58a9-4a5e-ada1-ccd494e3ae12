package com.zqn.modeldata2.controller.export;

import cn.afterturn.easypoi.cache.ExcelCache;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.zqn.modeldata2.entity.sop.SopExcelExport;
import com.zqn.modeldata2.entity.sop.SopFlowPicture;
import com.zqn.modeldata2.service.SopService;
import com.zqn.sop.util.ExcelExportUtil;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

@Controller
@RequestMapping("/sop/lm/xmexport")
public class SopLMXmExportController {

    @Autowired
    private SopService sopService;

    @Value("${uploadUrl}")
    private String TARGET_FOLDER;

    @GetMapping("/excel")
    public void exportOperationProcess(
            @RequestParam(value = "model") String model,
            @RequestParam(value = "operation") String operation,
            @RequestParam(value = "rtgCode") String rtgCode,
            HttpServletResponse response
    ) throws IOException {
        String filePath = "static/excel/lm/xm.xlsx";
        Workbook tempWorkbook = ExcelCache.getWorkbook(filePath, new Integer[]{0}, false);

        List<SopExcelExport> exportList = sopService.getExcelExportList(model, operation, rtgCode);

        Map<Integer, List<Map<String, Object>>> resultMap = new TreeMap<>();
        List<Map<String, Object>> mapList = new ArrayList<>();

        for (int sheetIndex = exportList.size() - 1; sheetIndex >= 0; sheetIndex--) {
            SopExcelExport excelExport = exportList.get(sheetIndex);

            Map<String, Object> map = new TreeMap<>();
            map.put("modelDesc", excelExport.getModelDesc());
            map.put("model", excelExport.getModel());
            map.put("item", excelExport.getSkey());
            map.put("actions", excelExport.getActions());
            map.put("standard", excelExport.getStandard() != null ? excelExport.getStandard().trim() : "");
            map.put("machine", excelExport.getMachine() != null ? excelExport.getMachine().trim() : "");
            map.put("needle", excelExport.getNeedle() != null ? excelExport.getNeedle().trim() : "");
            map.put("carLine", excelExport.getCarLine() != null ? excelExport.getCarLine().trim() : "");
            map.put("margin", excelExport.getMargin() != null ? excelExport.getMargin().trim() : "");
            map.put("needleSpacing", excelExport.getNeedleSpacing() != null ? excelExport.getNeedleSpacing().trim() : "");
            map.put("tools", excelExport.getTools() != null ? excelExport.getTools().trim() : "");
            map.put("glue", excelExport.getGlue() != null ? excelExport.getGlue().trim() : "");
            map.put("checkSelfPoint", excelExport.getCheckPoint() != null ? excelExport.getCheckPoint().trim() : "");
            map.put("empty", "");

            mapList.add(map);
        }

        resultMap.put(0, mapList);

        TemplateExportParams params = new TemplateExportParams(filePath, true);
        params.setTemplateWb(tempWorkbook);
        XSSFWorkbook workbook = (XSSFWorkbook) ExcelExportUtil.exportExcelClone(resultMap, params);

        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            settingPrintSetup(workbook.getSheetAt(i), (short) 80);
            workbook.setSheetName(i, "Stitching Instructions " + (i + 1));
        }

        for (int i = 0; i < exportList.size(); i++) {
            SopExcelExport excelExport = exportList.get(i);
            List<SopFlowPicture> assetsPicture = excelExport.getImgList();

            if (!CollectionUtils.isEmpty(assetsPicture)) {
                List<byte[]> imageList = new ArrayList<>();

                for (SopFlowPicture sopFlowPicture : assetsPicture) {
                    String picturePath = TARGET_FOLDER + sopFlowPicture.getImgUrl();
                    byte[] compressedImage = compressImage(picturePath);
                    imageList.add(compressedImage);
                }

                XSSFSheet sheet = workbook.getSheet("Stitching Instructions " + (i + 1));
                XSSFDrawing patriarch = sheet.createDrawingPatriarch();
                insertImages(patriarch, workbook, imageList, 14, 1, 7, 6);
            }
        }

        String fileName = model + "-鞋面-" + rtgCode + ".xlsx";

        response.reset();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*"); // 或者指定具体的域名
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        try {
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 压缩图片
    public static byte[] compressImage(String inputImagePath) {
        try {
            // 读取图像
            BufferedImage bufferImg = ImageIO.read(new File(inputImagePath));

            // 获取图片格式
            String formatName = inputImagePath.substring(inputImagePath.lastIndexOf(".") + 1);
            if (formatName.equals("jfif")) {
                formatName = "jpeg";
            }

            // 获取图片写入器
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(formatName);
            if (!writers.hasNext()) throw new IllegalStateException("No writers found");
            ImageWriter writer = writers.next();

            // 设置输出流
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            ImageOutputStream ios = ImageIO.createImageOutputStream(byteArrayOut);
            writer.setOutput(ios);

            // 设置压缩参数
            ImageWriteParam param = writer.getDefaultWriteParam();
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.2f); // 这里设置压缩质量
            }

            // 写入图像
            writer.write(null, new IIOImage(bufferImg, null, null), param);

            // 关闭流
            ios.close();
            writer.dispose();

            return byteArrayOut.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    // 插入图片
    private void insertImages(XSSFDrawing patriarch, XSSFWorkbook workbook, List<byte[]> imageBytesList, int startRow, int startCol, int rowHeight, int colWidth) {
        for (int i = 0; i < imageBytesList.size(); i++) {
            int col1 = startCol + (i % 8) * colWidth + (i % 8 + 1);
            int row1 = startRow + (i / 8) * (rowHeight + 1);
            int col2 = col1 + colWidth;
            int row2 = row1 + rowHeight;
            byte[] imageBytes = imageBytesList.get(i);
            XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, col1, row1, col2, row2);
            patriarch.createPicture(anchor, workbook.addPicture(imageBytes, XSSFWorkbook.PICTURE_TYPE_PNG));
        }
    }

    private void settingPrintSetup(Sheet targetSheet, short scale) {
        PrintSetup targetPrintSetup = targetSheet.getPrintSetup();
        targetPrintSetup.setPaperSize((short) 9);
        targetPrintSetup.setScale(scale);
        targetPrintSetup.setLandscape(true);
    }
}
