package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.sop.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * SOP 持久层接口
 */
@Mapper
public interface SopMapper {
    List<SopOperation> getOperationList();

    List<String> getBrandList();

    List<SopModel> getModelList(String brand);

    List<String> getAllModelList();

    SopPicture getModelPicture(String model);

    List<String> getInfoTypeList(String operation);

    List<SopMaterial> getInfoMaterialList();

    List<SopMaterial> getInfoMaterialList1(String model);

    List<SopMaterial> getInfoMaterialList2(String model);

    List<String> getLastNosList(String brand);

    List<String> getOsNoList(String brand);

    List<SopProcessInfo> getProcessInfoList(SopProcessInfo sopProcessInfo);

    int updateInfoType(SopProcessInfo sopProcessInfo);

    int updateInfoMaterial(SopProcessInfo sopProcessInfo);

    int addProcessInfo(SopProcessInfo sopProcessInfo);

    SopBackupInfo getBackupInfo(SopProcessInfo sopProcessInfo);

    int addBackupInfo(SopBackupInfo sopBackupInfo);

    int deleteProcessInfo(List<SopProcessInfo> processInfoList);

    List<SopProcessFlow> getProcessFlowList(SopProcessFlow sopProcessFlow);

    int addProcessFlow(SopProcessFlow sopProcessFlow);

    int insertProcessFlow(SopProcessFlow sopProcessFlow);

    int sequencePlus(SopProcessFlow sopProcessFlow);

    List<SopTemplate> getTemplateList(String operation);

    int templateImportFlow(SopProcessFlow sopProcessFlow);

    int updateFlowSection(SopProcessFlow sopProcessFlow);

    int sequencePlusOne(SopProcessFlow sopProcessFlow);

    int sequenceMinusOne(SopProcessFlow sopProcessFlow);

    int updateFlowSequence(SopProcessFlow sopProcessFlow);

    SopFlowDetail getFlowDetail(SopProcessFlow sopProcessFlow);

    List<SopFlowPicture> getFlowPictureList(SopProcessFlow sopProcessFlow);

    SopFlowDetail getFlowStandard(SopProcessFlow sopProcessFlow);

    SopFlowDetail getFlowCheckPoint(SopProcessFlow sopProcessFlow);

    int updateFlowDetail(SopFlowDetail sopFlowDetail);

    List<SopFlowPicture> getFlowPictureList(SopFlowDetail sopFlowDetail);

    List<SopBackupImage> getBackupImage(SopProcessFlow sopProcessFlow);

    int addBackupImage(SopBackupImage sopBackupImage);

    int deleteFlowPicture(SopFlowDetail sopFlowDetail);

    int addFlowPicture(String model, String operation, String rtgCode, String seqNo, String imgUrl);

    List<SopBackupContent> getBackupContent(SopProcessFlow sopProcessFlow);

    int addBackupContent(SopBackupContent sopBackupContent);

    int deleteStandardAndCheckPoint(SopFlowDetail sopFlowDetail);

    int addFlowStandard(SopFlowDetail sopFlowDetail);

    int addFlowCheckPoint(SopFlowDetail sopFlowDetail);

    List<SopBackupDetail> getBackupDetail(SopProcessFlow sopProcessFlow);

    int addBackupDetail(SopBackupDetail sopBackupDetail);

    int deleteProcessFlow(List<SopProcessFlow> processFlowList);

    List<SopFlowPicture> getAllFlowPictureList(SopProcessInfo sopProcessInfo);

    int resetFlowPicture(SopProcessInfo sopProcessInfo);

    int resetStandardAndCheckPoint(SopProcessInfo sopProcessInfo);

    int resetProcessFlow(SopProcessInfo sopProcessInfo);

    List<String> getFlowOptionList(Integer type, Integer dept);

    List<SopFlowAction> getFlowActionList(String proSeq);

    List<SopActionDetail> selectFlowAction(long id);

    List<SopPreview> getPreviewList(String model, String operation, String rtgCode);

    String getMaxRtgCode(String model, String operation);

    int copyProcessFlow(SopPreview sopPreview);

    int copyStandardAndCheckPoint(String targetModel, String targetRtgCode, String model, String operation, String rtgCode);

    List<SopFlowDetail> getOverallFlow(String model, String operation, String rtgCode);

    int addOverallFlow(SopProcessFlow sopProcessFlow);

    String getMaxSeqNo(String model, String operation, String rtgCode);

    int updateCompleteState(String model, String operation, String rtgCode, String cplFlag);

    List<SopCreator> getCreatorList();

    List<String> getOperationOption();

    List<SopTagOption> getTagOption();

    List<String> getTranslationModelList(boolean isCompleted);

    List<String> getTranslationOperationList(boolean isCompleted);

    List<String> getTranslationProSeqList(boolean isCompleted);

    List<SopTranslation> getTranslationList(String model, String operation, String proSeq, int pageNo, int pageSize, boolean isCompleted, boolean needTranslation);

    int getTranslationCount();

    int updateTranslation(SopTranslation sopTranslation);

    int addTranslation(SopTranslation sopTranslation);

    List<SopPress> getPressList(String model_no, String operation, String rtg_code, String component_name);

    int addPress(SopPress sopPress);

    int updatePress(SopPress sopPress);

    int addBackupPress(SopPress sopPress);

    int deletePress(List<SopProcessInfo> processInfoList);

    int copyPress(String targetModel, String targetRtgCode, boolean isCopyPicture, String model, String operation, String rtgCode);

    SopSku getSku(String proNo);

    List<SopSku> getSkuList(String model);

    int updateInfoSku(SopProcessInfo sopProcessInfo);

    List<String> getPartList();

    List<SopExcelExport> getExcelExportList(String model, String operation, String rtgCode, String partName);

    List<String> getExistingPartList(String model, String operation, String rtgCode);
}
