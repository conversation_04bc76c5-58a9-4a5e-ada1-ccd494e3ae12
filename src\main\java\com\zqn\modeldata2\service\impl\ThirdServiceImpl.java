package com.zqn.modeldata2.service.impl;

import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.zqn.modeldata2.entity.CkSmodelpa;
import com.zqn.modeldata2.mapper.ThirdMapper;
import com.zqn.modeldata2.service.ThirdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ThirdServiceImpl implements ThirdService {
    @Autowired
    private ThirdMapper thirdMapper;

    @Override
    public Map<String, List<Object>> getOption() {
        Map<String, List<Object>> map = new HashMap<>();
        map.put("option1List", thirdMapper.getOption1());
        map.put("option2List", thirdMapper.getOption2());
        return map;
    }

    @Override
    public Map<String, List<CkSmodelpa>> getPart(CkSmodelpa ckSmodelpa) {
        Map<String, List<CkSmodelpa>> map = new HashMap<>();
        map.put("part1List", thirdMapper.getPart1(ckSmodelpa));
        map.put("part2List", thirdMapper.getPart2(ckSmodelpa));
        return map;
    }

    @Override
    public int addTemporaryPart(CkSmodelpa ckSmodelpa) {
        int result = -1;
        try {
            if (ckSmodelpa.getPart_name() != null && ckSmodelpa.getPart_name().length() > 0) {
                ckSmodelpa.setPart_name(ZhConverterUtil.toTraditional(ckSmodelpa.getPart_name()));
            }
            result = thirdMapper.addTemporaryPart(ckSmodelpa);
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public int addPart(List<CkSmodelpa> ckSmodelpaList) {
        int result = -1;
        try {
            for (CkSmodelpa ckSmodelpa : ckSmodelpaList) {
                thirdMapper.addTemporaryPart(ckSmodelpa);
            }
            result = 1;
//            result = thirdMapper.addAllPart(ckSmodelpaList);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public int updatePart(CkSmodelpa ckSmodelpa) {
        int result = -1;
        try {
            result = thirdMapper.updatePart(ckSmodelpa);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public int deletePart(CkSmodelpa ckSmodelpa) {
        int result = -1;
        try {
            result = thirdMapper.deletePart(ckSmodelpa);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public int batchDeletePart(List<CkSmodelpa> ckSmodelpaList) {
        int result = -1;
        try {
            result = thirdMapper.batchDeletePart(ckSmodelpaList);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }
}
