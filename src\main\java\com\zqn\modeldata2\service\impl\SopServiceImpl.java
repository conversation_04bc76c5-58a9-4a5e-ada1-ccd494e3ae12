package com.zqn.modeldata2.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.zqn.modeldata2.entity.sop.*;
import com.zqn.modeldata2.mapper.SopMapper;
import com.zqn.modeldata2.service.SopService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * SOP 服务层
 */
@Slf4j
@Service
public class SopServiceImpl implements SopService {
    @Value("${uploadUrl}")
    private String targetFolder;

    @Value("${file.prefix}")
    private String filePrefix;

    @Autowired
    private SopMapper sopMapper;

    @Override
    public List<SopOperation> getOperationList() {
        List<SopOperation> result = new ArrayList<>();
        try {
            result = sopMapper.getOperationList();
        } catch (Exception e) {
            log.error("SOP - 获取制程列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopBrand> getBrandList() {
        String[] initialArray = new String[] {
                "A", "B", "C", "D", "E", "F", "G",
                "H", "I", "J", "K", "L", "M", "N",
                "O", "P", "Q",
                "R", "S", "T",
                "U", "V", "W", "X", "Y", "Z"
        };
        List<SopBrand> result = null;
        try {
            List<String> brandList = sopMapper.getBrandList();
            result = new ArrayList<>();
            for (String initial : initialArray) {
                SopBrand sopBrand = new SopBrand();
                sopBrand.setInitial(initial);
                sopBrand.setBrandList(new ArrayList<>());
                for (String brand : brandList) {
                    if (brand.startsWith(initial)) {
                        sopBrand.getBrandList().add(brand);
                    }
                }
                result.add(sopBrand);
            }
        } catch (Exception e) {
            log.error("SOP - 获取品牌列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopModel> getModelList(String brand) {
        List<SopModel> result = new ArrayList<>();
        try {
            result = sopMapper.getModelList(brand);
        } catch (Exception e) {
            log.error("SOP - 获取型体列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getAllModelList() {
        List<String> result = new ArrayList<>();
        try {
            result = sopMapper.getAllModelList();
        } catch (Exception e) {
            log.error("SOP - 获取所有型体列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public SopPicture getModelPicture(String model) {
        SopPicture result = null;
        try {
            result = sopMapper.getModelPicture(model);
        } catch (Exception e) {
            log.error("SOP - 获取型体鞋图 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getInfoTypeList(String operation) {
        List<String> result = new ArrayList<>();
        try {
            result = sopMapper.getInfoTypeList(operation);
        } catch (Exception e) {
            log.error("SOP - 获取工序信息型体列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopMaterial> getInfoMaterialList(String model, String operation) {
        List<SopMaterial> result = new ArrayList<>();
        try {
            if ("4".equals(operation)) {
                result = sopMapper.getInfoMaterialList1(model);
            } else if ("5".equals(operation) || "6".equals(operation) || "8".equals(operation)) {
                result = sopMapper.getInfoMaterialList2(model);
            }
            if (result == null || result.isEmpty()) {
                result = sopMapper.getInfoMaterialList();
            }
        } catch (Exception e) {
            log.error("SOP - 获取工序信息材质列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getLastNosList(String brand) {
        List<String> result = new ArrayList<>();
        try {
            result = sopMapper.getLastNosList(brand);
        } catch (Exception e) {
            log.error("SOP - 获取楦头编号列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getOsNoList(String brand) {
        List<String> result = new ArrayList<>();
        try {
            result = sopMapper.getOsNoList(brand);
        } catch (Exception e) {
            log.error("SOP - 获取 Outsole 列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopProcessInfo> getProcessInfoList(SopProcessInfo sopProcessInfo) {
        List<SopProcessInfo> result = new ArrayList<>();
        try {
            result = sopMapper.getProcessInfoList(sopProcessInfo);
            for (SopProcessInfo processInfo : result) {
                if (processInfo.getProNos() != null && !processInfo.getProNos().isEmpty()) {
                    List<SopSku> skuList = new ArrayList<>();
                    String[] proNoArray = processInfo.getProNos().split(";");
                    for (String proNo : proNoArray) {
                        if (proNo != null && !proNo.isEmpty() && sopMapper.getSku(proNo) != null) {
                            skuList.add(sopMapper.getSku(proNo));
                        }
                    }
                    processInfo.setSkuList(skuList);
                }
            }
        } catch (Exception e) {
            log.error("SOP - 获取工序信息列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int updateInfoType(SopProcessInfo sopProcessInfo) {
        int result = -1;
        try {
            result = sopMapper.updateInfoType(sopProcessInfo);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 修改工序信息生产类型 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int updateInfoMaterial(SopProcessInfo sopProcessInfo) {
        int result = -1;
        try {
            result = sopMapper.updateInfoMaterial(sopProcessInfo);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 修改工序信息材质 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int addProcessInfo(SopProcessInfo sopProcessInfo) {
        int result = -1;
        try {
            result = sopMapper.addProcessInfo(sopProcessInfo);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 添加工序信息 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
//    @Transactional
    @DSTransactional
    public int deleteProcessInfo(List<SopProcessInfo> processInfoList) {
        int result = -1;
        try {
            for (SopProcessInfo sopProcessInfo : processInfoList) {
                SopBackupInfo sopBackupInfo = sopMapper.getBackupInfo(sopProcessInfo);
                if (sopBackupInfo != null) {
                    DynamicDataSourceContextHolder.push("app");
                    sopMapper.addBackupInfo(sopBackupInfo);
                    DynamicDataSourceContextHolder.clear();
                    DynamicDataSourceContextHolder.push("master");
                }

                String model_no = sopProcessInfo.getModel();
                String operation = sopProcessInfo.getOperation();
                String rtg_code = sopProcessInfo.getRtgCode();
                List<SopPress> pressList = sopMapper.getPressList(model_no, operation, rtg_code, null);
                for (SopPress sopPress : pressList) {
                    DynamicDataSourceContextHolder.push("app");
                    sopMapper.addBackupPress(sopPress);
                    DynamicDataSourceContextHolder.clear();
                    DynamicDataSourceContextHolder.push("master");
                }
            }

            sopMapper.deleteProcessInfo(processInfoList);
            sopMapper.deletePress(processInfoList);
            result = 1;
        } catch (Exception e) {
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            log.error("SOP - 删除工序信息 功能报错！{}", e.getMessage());
            throw new RuntimeException("SOP - 删除工序信息 功能报错！" + e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopProcessFlow> getProcessFlowList(SopProcessFlow sopProcessFlow) {
        List<SopProcessFlow> result = new ArrayList<>();
        try {
            result = sopMapper.getProcessFlowList(sopProcessFlow);
        } catch (Exception e) {
            log.error("SOP - 获取工序流程列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int addProcessFlow(SopProcessFlow sopProcessFlow) {
        int result = -1;
        try {
            result = sopMapper.addProcessFlow(sopProcessFlow);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 添加工序流程 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int insertProcessFlow(SopProcessFlow sopProcessFlow) {
        int result = -1;
        try {
            sopMapper.sequencePlus(sopProcessFlow);
            sopMapper.insertProcessFlow(sopProcessFlow);
            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 插入工序流程 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopTemplate> getTemplateList(String operation) {
        List<SopTemplate> result = new ArrayList<>();
        try {
            result = sopMapper.getTemplateList(operation);
        } catch (Exception e) {
            log.error("SOP - 获取模板列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public int templateImportFlow(SopProcessFlow sopProcessFlow) {
        int result = -1;
        try {
            result = sopMapper.templateImportFlow(sopProcessFlow);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 模板导入工序流程 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public int updateFlowSection(SopProcessFlow sopProcessFlow) {
        int result = -1;
        try {
            result = sopMapper.updateFlowSection(sopProcessFlow);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 修改工序流程加工段 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int sortSection(SopProcessFlow sopProcessFlow) {
        int result = -1;
        try {
            String startWkGroup = sopProcessFlow.getStartWkGroup();
            List<SopProcessFlow> list = sopMapper.getProcessFlowList(sopProcessFlow);
            int number = 0;
            for (int i = 0; i < list.size(); i++) {
                SopProcessFlow sopProcessFlow1 = list.get(i);
                if (sopProcessFlow1.getWkGroup() == null || Integer.parseInt(sopProcessFlow1.getWkGroup()) >= Integer.parseInt(startWkGroup)) {
                    sopProcessFlow1.setTargetWkGroup(String.format("%03d", Integer.parseInt(startWkGroup) + number));
                    sopMapper.updateFlowSection(sopProcessFlow1);
                    number++;
                }
            }
            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 工序流程加工段排序 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int updateFlowSequence(SopProcessFlow sopProcessFlow) {
        int result = -1;
        try {
            if (sopProcessFlow.getSkey() > sopProcessFlow.getTargetSkey()) {
                sopMapper.sequencePlusOne(sopProcessFlow);
            } else if (sopProcessFlow.getSkey() < sopProcessFlow.getTargetSkey()) {
                sopMapper.sequenceMinusOne(sopProcessFlow);
            }
            result = sopMapper.updateFlowSequence(sopProcessFlow);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 修改工序流程序号 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public SopFlowDetail getFlowDetail(SopProcessFlow sopProcessFlow) {
        SopFlowDetail result = null;
        try {
            result = sopMapper.getFlowDetail(sopProcessFlow);
            List<SopFlowPicture> imgList = sopMapper.getFlowPictureList(sopProcessFlow);
            SopFlowDetail standard = sopMapper.getFlowStandard(sopProcessFlow);
            SopFlowDetail checkPoint = sopMapper.getFlowCheckPoint(sopProcessFlow);
            if (imgList != null) {
                result.setImgList(imgList);
            }
            if (standard != null) {
                result.setStandardId(standard.getStandardId());
                result.setStandard(standard.getStandard());
            }
            if (checkPoint != null) {
                result.setCheckPointId(checkPoint.getCheckPointId());
                result.setCheckPoint(checkPoint.getCheckPoint());
            }
        } catch (Exception e) {
            log.error("SOP - 获取工序流程详情 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int saveFlowDetail(SopFlowDetail sopFlowDetail) {
        int result = -1;
        try {
            String seqName = "";
            if ("4".equals(sopFlowDetail.getOperation())) {
                // 鞋面
                // 工序名称 = 操作标准 + 胶水 + 车线 + 边距 + 针距 + 间距 + 车针
                if (sopFlowDetail.getStandard() != null && !sopFlowDetail.getStandard().isEmpty()) {
                    seqName += sopFlowDetail.getStandard();
                }
                if (sopFlowDetail.getGlue() != null && !sopFlowDetail.getGlue().isEmpty()) {
                    seqName += " " + sopFlowDetail.getGlue().replace("\n", " ");
                }
                if (sopFlowDetail.getCarLine() != null && !sopFlowDetail.getCarLine().isEmpty()) {
                    seqName += " " + sopFlowDetail.getCarLine().replace("\n", " ");
                }
                if (sopFlowDetail.getMargin() != null && !sopFlowDetail.getMargin().isEmpty()) {
                    seqName += " " + sopFlowDetail.getMargin().replace("\n", " ");
                }
                if (sopFlowDetail.getNeedleSpacing() != null && !sopFlowDetail.getNeedleSpacing().isEmpty()) {
                    seqName += " " + sopFlowDetail.getNeedleSpacing().replace("\n", " ");
                }
                if (sopFlowDetail.getSpacing() != null && !sopFlowDetail.getSpacing().isEmpty()) {
                    seqName += " " + sopFlowDetail.getSpacing().replace("\n", " ");
                }
                if (sopFlowDetail.getNeedle() != null && !sopFlowDetail.getNeedle().isEmpty()) {
                    seqName += " " + sopFlowDetail.getNeedle().replace("\n", " ");
                }
                if (seqName.isEmpty()) {
                    if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                        seqName = sopFlowDetail.getActions();
                    } else {
                        seqName = "新工序";
                    }
                }
            } else if ("5".equals(sopFlowDetail.getOperation()) || "6".equals(sopFlowDetail.getOperation()) || "8".equals(sopFlowDetail.getOperation())) {
                // 半成品、成型、皮底
                // 工序名称 = 动作 + 化学品 + 温度 + 压力 + 时间
                if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                    seqName += sopFlowDetail.getActions();
                }
                if (sopFlowDetail.getChemical() != null && !sopFlowDetail.getChemical().isEmpty()) {
                    seqName += " " + sopFlowDetail.getChemical().replace("\n", " ");
                }
                if (sopFlowDetail.getTemp() != null && !sopFlowDetail.getTemp().isEmpty()) {
                    seqName += " " + sopFlowDetail.getTemp().replace("\n", " ");
                }
                if (sopFlowDetail.getPressure() != null && !sopFlowDetail.getPressure().isEmpty()) {
                    seqName += " " + sopFlowDetail.getPressure().replace("\n", " ");
                }
                if (sopFlowDetail.getTime() != null && !sopFlowDetail.getTime().isEmpty()) {
                    seqName += " " + sopFlowDetail.getTime().replace("\n", " ");
                }
                if (seqName.isEmpty()) {
                    seqName = "新工序";
                }
                if (sopFlowDetail.getStandard() != null && !sopFlowDetail.getStandard().isEmpty()) {
                    sopFlowDetail.setRemark(sopFlowDetail.getStandard());
                }
            } else if ("1".equals(sopFlowDetail.getOperation())) {
                // 加工
                // 工序名称 = 动作 + 选项
                if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                    seqName += sopFlowDetail.getActions();
                }
                for (int i = 1; i <= 10; i++) {
                    String methodName = "getProcessOption" + i;
                    try {
                        String methodValue = (String) sopFlowDetail.getClass().getMethod(methodName).invoke(sopFlowDetail);
                        if (methodValue != null && !methodValue.isEmpty()) {
                            seqName += " " + methodValue.replace("\n", " ");
                        }
                    } catch (Exception e) {
                        log.error("SOP - 修改整体流程功能报错！{}", e.getMessage());
                    }
                }
                if (seqName.isEmpty()) {
                    seqName = "新工序";
                }
                if (sopFlowDetail.getStandard() != null && !sopFlowDetail.getStandard().isEmpty()) {
                    sopFlowDetail.setRemark(sopFlowDetail.getStandard());
                }
            } else {
                // 其它
                if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                    seqName += sopFlowDetail.getActions();
                }
                if (seqName.isEmpty()) {
                    seqName = "新工序";
                }
            }
            sopFlowDetail.setSeqName(seqName);
            sopMapper.updateFlowDetail(sopFlowDetail);
            sopMapper.deleteStandardAndCheckPoint(sopFlowDetail);
            sopMapper.addFlowStandard(sopFlowDetail);
            sopMapper.addFlowCheckPoint(sopFlowDetail);

            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH");
            String format = sdf.format(date);

            String imgPath = targetFolder + File.separator + "img" + File.separator + format;

//            List<SopFlowPicture> imgList = sopMapper.getFlowPictureList(sopFlowDetail);
            List<SopFlowPicture> imgList1 = sopFlowDetail.getImgList();
            List<SopFlowPicture> imgList2 = new ArrayList<>();

            sopMapper.deleteFlowPicture(sopFlowDetail);
//            for (SopFlowPicture img : imgList) {
//                boolean flag = false;
//                for (SopFlowPicture img1 : imgList1) {
//                    if (img.getId() == img1.getId()) {
//                        flag = true;
//                        break;
//                    }
//                }
//                if (!flag) {
//                    FileUtil.del(targetFolder + img.getImgUrl());
//                }
//            }

            for (SopFlowPicture img : imgList1) {
                if (img.getId() < 0) {
                    if (!FileUtil.exist(imgPath)) {
                        FileUtil.mkdir(imgPath);
                    }

                    // Base64 字符串
                    String base64 = img.getImgUrl();
                    // 扩展名
                    String extension = base64.substring(base64.indexOf("/") + 1, base64.indexOf(";"));
                    // 主要数据
                    String data = base64.substring(base64.indexOf(",") + 1);
                    // 随机生成文件名
                    String fileName = IdUtil.randomUUID() + "." + extension;

                    byte[] bytes = Base64.getDecoder().decode(data);
                    Path path = Paths.get(imgPath + File.separator + fileName);
                    Files.write(path, bytes);

                    String fileDownloadUri = "/img/" + format + "/" + fileName;
                    img.setImgUrl(fileDownloadUri);
                    imgList2.add(img);
                } else {
                    imgList2.add(img);
                }
            }
            sopFlowDetail.setImgList(imgList2);
            for (SopFlowPicture img : imgList2) {
                sopMapper.addFlowPicture(sopFlowDetail.getModel(), sopFlowDetail.getOperation(), sopFlowDetail.getRtgCode(), sopFlowDetail.getSeqNo(), img.getImgUrl());
            }

            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 保存工序流程详情 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
//    @Transactional
    @DSTransactional
    public int deleteProcessFlow(List<SopProcessFlow> processFlowList) {
        int result = -1;
        try {
            if (processFlowList.isEmpty()) {
                return result;
            }

            SopProcessFlow spf = new SopProcessFlow();
            spf.setModel(processFlowList.get(0).getModel());
            spf.setOperation(processFlowList.get(0).getOperation());
            spf.setRtgCode(processFlowList.get(0).getRtgCode());
            List<SopProcessFlow> list1 = sopMapper.getProcessFlowList(spf);

            for (SopProcessFlow processFlow : processFlowList) {
                List<SopBackupContent> sopBackupContentList = sopMapper.getBackupContent(processFlow);
                for (SopBackupContent sopBackupContent : sopBackupContentList) {
                    DynamicDataSourceContextHolder.push("app");
                    sopMapper.addBackupContent(sopBackupContent);
                    DynamicDataSourceContextHolder.clear();
                    DynamicDataSourceContextHolder.push("master");
                }

                List<SopBackupImage> sopBackupImageList = sopMapper.getBackupImage(processFlow);
                for (SopBackupImage sopBackupImage : sopBackupImageList) {
                    DynamicDataSourceContextHolder.push("app");
                    sopMapper.addBackupImage(sopBackupImage);
                    DynamicDataSourceContextHolder.clear();
                    DynamicDataSourceContextHolder.push("master");
                }

                SopFlowDetail sopFlowDetail = new SopFlowDetail();
                sopFlowDetail.setModel(processFlow.getModel());
                sopFlowDetail.setOperation(processFlow.getOperation());
                sopFlowDetail.setRtgCode(processFlow.getRtgCode());
                sopFlowDetail.setSeqNo(processFlow.getSeqNo());

//                List<SopFlowPicture> imgList = sopMapper.getFlowPictureList(sopFlowDetail);
                sopMapper.deleteStandardAndCheckPoint(sopFlowDetail);
                sopMapper.deleteFlowPicture(sopFlowDetail);
//                for (SopFlowPicture img : imgList) {
//                    FileUtil.del(targetFolder + img.getImgUrl());
//                }
            }

            for (SopProcessFlow sopProcessFlow : processFlowList) {
                List<SopBackupDetail> sopBackupDetailList = sopMapper.getBackupDetail(sopProcessFlow);
                for (SopBackupDetail sopBackupDetail : sopBackupDetailList) {
                    DynamicDataSourceContextHolder.push("app");
                    sopMapper.addBackupDetail(sopBackupDetail);
                    DynamicDataSourceContextHolder.clear();
                    DynamicDataSourceContextHolder.push("master");
                }
            }
            sopMapper.deleteProcessFlow(processFlowList);

            List<SopProcessFlow> list2 = sopMapper.getProcessFlowList(spf);
            if (list1 != null && list2 != null && list1.size() != list2.size()) {
                for (int i = 0; i < list2.size(); i++) {
                    SopProcessFlow spf1 = list2.get(i);
                    spf1.setTargetSkey(i + 1);
                    sopMapper.updateFlowSequence(spf1);
                }
            }

            result = 1;
        } catch (Exception e) {
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            log.error("SOP - 删除工序流程 功能报错！{}", e.getMessage());
            throw new RuntimeException("SOP - 删除工序流程 功能报错！" + e.getMessage());
        }
        return result;
    }

    @Override
//    @Transactional
    @DSTransactional
    public int resetProcessFlow(SopProcessInfo sopProcessInfo) {
        int result = -1;
        try {
            SopProcessFlow sopProcessFlow = new SopProcessFlow();
            sopProcessFlow.setModel(sopProcessInfo.getModel());
            sopProcessFlow.setOperation(sopProcessInfo.getOperation());
            sopProcessFlow.setRtgCode(sopProcessInfo.getRtgCode());

            List<SopBackupContent> sopBackupContentList = sopMapper.getBackupContent(sopProcessFlow);
            List<SopBackupImage> sopBackupImageList = sopMapper.getBackupImage(sopProcessFlow);
            List<SopBackupDetail> sopBackupDetailList = sopMapper.getBackupDetail(sopProcessFlow);

            DynamicDataSourceContextHolder.push("app");
            for (SopBackupContent sopBackupContent : sopBackupContentList) {
                sopMapper.addBackupContent(sopBackupContent);
            }
            for (SopBackupImage sopBackupImage : sopBackupImageList) {
                sopMapper.addBackupImage(sopBackupImage);
            }
            for (SopBackupDetail sopBackupDetail : sopBackupDetailList) {
                sopMapper.addBackupDetail(sopBackupDetail);
            }
            DynamicDataSourceContextHolder.clear();
            DynamicDataSourceContextHolder.push("master");

//            List<SopFlowPicture> imgList = sopMapper.getAllFlowPictureList(sopProcessInfo);
            sopMapper.resetStandardAndCheckPoint(sopProcessInfo);
            sopMapper.resetFlowPicture(sopProcessInfo);
            sopMapper.resetProcessFlow(sopProcessInfo);

//            for (SopFlowPicture img : imgList) {
//                FileUtil.del(targetFolder + img.getImgUrl());
//            }

            result = 1;
        } catch (Exception e) {
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            log.error("SOP - 重置工序流程 功能报错！{}", e.getMessage());
            throw new RuntimeException("SOP - 重置工序流程 功能报错！" + e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getFlowOptionList(Integer type, Integer dept) {
        List<String> result = new ArrayList<>();
        try {
            result = sopMapper.getFlowOptionList(type, dept);
        } catch (Exception e) {
            log.error("SOP - 获取选项列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopFlowAction> getFlowActionList(String proSeq) {
        List<SopFlowAction> result = new ArrayList<>();
        try {
            result = sopMapper.getFlowActionList(proSeq);
        } catch (Exception e) {
            log.error("SOP - 获取动作列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public Map<String, String> selectFlowAction(long id) {
        Map<String, String> result = new HashMap<>();
        List<SopActionDetail> list = new ArrayList<>();
        try {
            list = sopMapper.selectFlowAction(id);
            for (SopActionDetail detail : list) {
                switch (detail.getType()) {
                    case 1:
                        result.put("standard", detail.getContent());
                        break;
                    case 2:
                        result.put("checkPoint", detail.getContent());
                        break;
                    case 3:
                        result.put("defence", detail.getContent());
                        break;
                    case 4:
                        result.put("tools", detail.getContent());
                        break;
                    case 5:
                        result.put("chemical", detail.getContent());
                        break;
                    case 6:
                        result.put("machine", detail.getContent());
                        break;
                    case 7:
                        result.put("temp", detail.getContent());
                        break;
                    case 8:
                        result.put("pressure", detail.getContent());
                        break;
                    case 9:
                        result.put("time", detail.getContent());
                        break;
                    case 10:
                        result.put("glue", detail.getContent());
                        break;
                    case 11:
                        result.put("carLine", detail.getContent());
                        break;
                    case 12:
                        result.put("needleSpacing", detail.getContent());
                        break;
                    case 13:
                        result.put("margin", detail.getContent());
                        break;
                    case 14:
                        result.put("needle", detail.getContent());
                        break;
                    case 15:
                        result.put("spacing", detail.getContent());
                        break;
                    case 311:
                    case 321:
                    case 331:
                    case 341:
                        result.put("processOption1", detail.getContent());
                        break;
                    case 312:
                    case 322:
                    case 332:
                    case 342:
                        result.put("processOption2", detail.getContent());
                        break;
                    case 313:
                    case 323:
                    case 333:
                    case 343:
                        result.put("processOption3", detail.getContent());
                        break;
                    case 314:
                    case 324:
                    case 334:
                    case 344:
                        result.put("processOption4", detail.getContent());
                        break;
                    case 315:
                    case 325:
                    case 335:
                    case 345:
                        result.put("processOption5", detail.getContent());
                        break;
                    case 326:
                    case 336:
                    case 346:
                        result.put("processOption6", detail.getContent());
                        break;
                    case 327:
                        result.put("processOption7", detail.getContent());
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("SOP - 选择动作 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopPreview> getPreviewList(String model, String operation, String rtgCode) {
        List<SopPreview> result = new ArrayList<>();
        try {
            result = sopMapper.getPreviewList(model, operation, rtgCode);
            for (SopPreview preview : result) {
                SopProcessFlow sopProcessFlow = new SopProcessFlow();
                sopProcessFlow.setModel(preview.getModel());
                sopProcessFlow.setOperation(preview.getOperation());
                sopProcessFlow.setRtgCode(preview.getRtgCode());
                sopProcessFlow.setSeqNo(preview.getSeqNo());
                List<SopFlowPicture> imgList = sopMapper.getFlowPictureList(sopProcessFlow);
                SopFlowDetail standard = sopMapper.getFlowStandard(sopProcessFlow);
                SopFlowDetail checkPoint = sopMapper.getFlowCheckPoint(sopProcessFlow);
                if (imgList != null) {
                    preview.setImgList(imgList);
                }
                if (standard != null) {
                    preview.setStandardId(standard.getStandardId());
                    preview.setStandard(standard.getStandard());
                }
                if (checkPoint != null) {
                    preview.setCheckPointId(checkPoint.getCheckPointId());
                    preview.setCheckPoint(checkPoint.getCheckPoint());
                }
            }
        } catch (Exception e) {
            log.error("SOP - 获取流程预览列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int copyProcess(SopProcessInfo sopProcessInfo, boolean isCopyPicture, List<String> targetModelList) {
        int result = -1;
        try {
            String sourceModel = sopProcessInfo.getModel();
            String operation = sopProcessInfo.getOperation();
            String proSeq = sopProcessInfo.getProSeq();
            String sourceRtgCode = sopProcessInfo.getRtgCode();
            List<SopPreview> sopPreviewList = sopMapper.getPreviewList(sourceModel, operation, sourceRtgCode);

            for (String targetModel : targetModelList) {
                sopProcessInfo.setModel(targetModel);
                sopMapper.addProcessInfo(sopProcessInfo);
                String targetRtgCode = sopMapper.getMaxRtgCode(targetModel, operation);
                for (SopPreview sopPreview : sopPreviewList) {
                    SopProcessFlow sopProcessFlow = new SopProcessFlow();
                    BeanUtils.copyProperties(sopPreview, sopProcessFlow);

                    sopPreview.setModel(targetModel);
                    sopPreview.setRtgCode(targetRtgCode);
                    sopPreview.setInsUser(sopProcessInfo.getInsUser());
                    sopPreview.setUpdUser(sopProcessInfo.getUpdUser());
                    sopMapper.copyProcessFlow(sopPreview);

                    if (isCopyPicture) {
                        List<SopFlowPicture> imgList = sopMapper.getFlowPictureList(sopProcessFlow);
                        for (SopFlowPicture img : imgList) {
                            Date date = new Date();
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH");
                            String format = sdf.format(date);

                            String sourceImg = img.getImgUrl();
                            String extension = img.getImgUrl().split("\\.").length > 1 ? img.getImgUrl().split("\\.")[1] : "jpg";
                            String targetImg = "/img/" + format + "/" + IdUtil.randomUUID() + "." + extension;

                            if (FileUtil.exist(targetFolder + File.separator + sourceImg)) {
                                sopMapper.addFlowPicture(targetModel, operation, targetRtgCode, sopProcessFlow.getSeqNo(), targetImg);
                                Path sourcePath = Paths.get(targetFolder + File.separator + sourceImg);
                                Path targetPath = Paths.get(targetFolder + File.separator + targetImg);
                                FileUtil.copy(sourcePath, targetPath);
                            }
                        }
                    }
                }
                sopMapper.copyStandardAndCheckPoint(targetModel, targetRtgCode, sourceModel, operation, sourceRtgCode);
                if ("1D".equals(proSeq) || "1E".equals(proSeq)) {
                    sopMapper.copyPress(targetModel, targetRtgCode, isCopyPicture, sourceModel, operation, sourceRtgCode);
                }
            }
            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 复制工序功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public List<SopFlowDetail> getOverallFlow(String model, String operation, String rtgCode) {
        List<SopFlowDetail> result = new ArrayList<>();
        SopProcessFlow sopProcessFlow = new SopProcessFlow();
        sopProcessFlow.setModel(model);
        sopProcessFlow.setOperation(operation);
        sopProcessFlow.setRtgCode(rtgCode);
        try {
            result = sopMapper.getOverallFlow(model, operation, rtgCode);
            for (SopFlowDetail sopFlowDetail : result) {
                sopProcessFlow.setSeqNo(sopFlowDetail.getSeqNo());
                List<SopFlowPicture> imgList = sopMapper.getFlowPictureList(sopProcessFlow);
                SopFlowDetail standard = sopMapper.getFlowStandard(sopProcessFlow);
                SopFlowDetail checkPoint = sopMapper.getFlowCheckPoint(sopProcessFlow);
                if (imgList != null) {
                    sopFlowDetail.setImgList(imgList);
                }
                if (standard != null) {
                    sopFlowDetail.setStandardId(standard.getStandardId());
                    sopFlowDetail.setStandard(standard.getStandard());
                }
                if (checkPoint != null) {
                    sopFlowDetail.setCheckPointId(checkPoint.getCheckPointId());
                    sopFlowDetail.setCheckPoint(checkPoint.getCheckPoint());
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 获取整体流程功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int selectOverallAction(long id, SopFlowDetail sopFlowDetail) {
        int result = -1;
        try {
            List<SopActionDetail> list = sopMapper.selectFlowAction(id);
            for (SopActionDetail detail : list) {
                switch (detail.getType()) {
                    case 1:
                        sopFlowDetail.setStandard(detail.getContent());
                        break;
                    case 2:
                        sopFlowDetail.setCheckPoint(detail.getContent());
                        break;
                    case 3:
                        sopFlowDetail.setDefence(detail.getContent());
                        break;
                    case 4:
                        sopFlowDetail.setTools(detail.getContent());
                        break;
                    case 5:
                        sopFlowDetail.setChemical(detail.getContent());
                        break;
                    case 6:
                        sopFlowDetail.setMachine(detail.getContent());
                        break;
                    case 7:
                        sopFlowDetail.setTemp(detail.getContent());
                        break;
                    case 8:
                        sopFlowDetail.setPressure(detail.getContent());
                        break;
                    case 9:
                        sopFlowDetail.setTime(detail.getContent());
                        break;
                    case 10:
                        sopFlowDetail.setGlue(detail.getContent());
                        break;
                    case 11:
                        sopFlowDetail.setCarLine(detail.getContent());
                        break;
                    case 12:
                        sopFlowDetail.setNeedleSpacing(detail.getContent());
                        break;
                    case 13:
                        sopFlowDetail.setMargin(detail.getContent());
                        break;
                    case 14:
                        sopFlowDetail.setNeedle(detail.getContent());
                        break;
                    case 15:
                        sopFlowDetail.setSpacing(detail.getContent());
                        break;
                    case 311:
                    case 321:
                    case 331:
                    case 341:
                        sopFlowDetail.setProcessOption1(detail.getContent());
                        break;
                    case 312:
                    case 322:
                    case 332:
                    case 342:
                        sopFlowDetail.setProcessOption2(detail.getContent());
                        break;
                    case 313:
                    case 323:
                    case 333:
                    case 343:
                        sopFlowDetail.setProcessOption3(detail.getContent());
                        break;
                    case 314:
                    case 324:
                    case 334:
                    case 344:
                        sopFlowDetail.setProcessOption4(detail.getContent());
                        break;
                    case 315:
                    case 325:
                    case 335:
                    case 345:
                        sopFlowDetail.setProcessOption5(detail.getContent());
                        break;
                    case 326:
                    case 336:
                    case 346:
                        sopFlowDetail.setProcessOption6(detail.getContent());
                        break;
                    case 327:
                        sopFlowDetail.setProcessOption7(detail.getContent());
                        break;
                    default:
                        break;
                }
            }
            String seqName = "";
            if ("4".equals(sopFlowDetail.getOperation())) {
                // 鞋面
                // 工序名称 = 操作标准 + 胶水 + 车线 + 边距 + 针距 + 间距 + 车针
                if (sopFlowDetail.getStandard() != null && !sopFlowDetail.getStandard().isEmpty()) {
                    seqName += sopFlowDetail.getStandard();
                }
                if (sopFlowDetail.getGlue() != null && !sopFlowDetail.getGlue().isEmpty()) {
                    seqName += " " + sopFlowDetail.getGlue().replace("\n", " ");
                }
                if (sopFlowDetail.getCarLine() != null && !sopFlowDetail.getCarLine().isEmpty()) {
                    seqName += " " + sopFlowDetail.getCarLine().replace("\n", " ");
                }
                if (sopFlowDetail.getMargin() != null && !sopFlowDetail.getMargin().isEmpty()) {
                    seqName += " " + sopFlowDetail.getMargin().replace("\n", " ");
                }
                if (sopFlowDetail.getNeedleSpacing() != null && !sopFlowDetail.getNeedleSpacing().isEmpty()) {
                    seqName += " " + sopFlowDetail.getNeedleSpacing().replace("\n", " ");
                }
                if (sopFlowDetail.getSpacing() != null && !sopFlowDetail.getSpacing().isEmpty()) {
                    seqName += " " + sopFlowDetail.getSpacing().replace("\n", " ");
                }
                if (sopFlowDetail.getNeedle() != null && !sopFlowDetail.getNeedle().isEmpty()) {
                    seqName += " " + sopFlowDetail.getNeedle().replace("\n", " ");
                }
                if (seqName.isEmpty()) {
                    if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                        seqName = sopFlowDetail.getActions();
                    } else {
                        seqName = "新工序";
                    }
                }
            } else if ("5".equals(sopFlowDetail.getOperation()) || "6".equals(sopFlowDetail.getOperation()) || "8".equals(sopFlowDetail.getOperation())) {
                // 半成品、成型、皮底
                // 工序名称 = 动作 + 化学品 + 温度 + 压力 + 时间
                if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                    seqName += sopFlowDetail.getActions();
                }
                if (sopFlowDetail.getChemical() != null && !sopFlowDetail.getChemical().isEmpty()) {
                    seqName += " " + sopFlowDetail.getChemical().replace("\n", " ");
                }
                if (sopFlowDetail.getTemp() != null && !sopFlowDetail.getTemp().isEmpty()) {
                    seqName += " " + sopFlowDetail.getTemp().replace("\n", " ");
                }
                if (sopFlowDetail.getPressure() != null && !sopFlowDetail.getPressure().isEmpty()) {
                    seqName += " " + sopFlowDetail.getPressure().replace("\n", " ");
                }
                if (sopFlowDetail.getTime() != null && !sopFlowDetail.getTime().isEmpty()) {
                    seqName += " " + sopFlowDetail.getTime().replace("\n", " ");
                }
                if (seqName.isEmpty()) {
                    seqName = "新工序";
                }
                if (sopFlowDetail.getStandard() != null && !sopFlowDetail.getStandard().isEmpty()) {
                    sopFlowDetail.setRemark(sopFlowDetail.getStandard());
                }
            } else if ("1".equals(sopFlowDetail.getOperation())) {
                // 加工
                // 工序名称 = 动作 + 选项
                if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                    seqName += sopFlowDetail.getActions();
                }
                for (int i = 1; i <= 10; i++) {
                    String methodName = "getProcessOption" + i;
                    try {
                        String methodValue = (String) sopFlowDetail.getClass().getMethod(methodName).invoke(sopFlowDetail);
                        if (methodValue != null && !methodValue.isEmpty()) {
                            seqName += " " + methodValue.replace("\n", " ");
                        }
                    } catch (Exception e) {
                        log.error("SOP - 修改整体流程功能报错！{}", e.getMessage());
                    }
                }
                if (seqName.isEmpty()) {
                    seqName = "新工序";
                }
                if (sopFlowDetail.getStandard() != null && !sopFlowDetail.getStandard().isEmpty()) {
                    sopFlowDetail.setRemark(sopFlowDetail.getStandard());
                }
            } else {
                // 其它
                if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                    seqName += sopFlowDetail.getActions();
                }
                if (seqName.isEmpty()) {
                    seqName = "新工序";
                }
            }
            sopFlowDetail.setSeqName(seqName);
            sopMapper.updateFlowDetail(sopFlowDetail);
            sopMapper.deleteStandardAndCheckPoint(sopFlowDetail);
            sopMapper.addFlowStandard(sopFlowDetail);
            sopMapper.addFlowCheckPoint(sopFlowDetail);
            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 选择整体流程动作功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int updateOverallFlow(SopFlowDetail sopFlowDetail) {
        System.out.println(sopFlowDetail);
        int result = -1;
        try {
            String seqName = "";
            if ("4".equals(sopFlowDetail.getOperation())) {
                // 鞋面
                // 工序名称 = 操作标准 + 胶水 + 车线 + 边距 + 针距 + 间距 + 车针
                if (sopFlowDetail.getStandard() != null && !sopFlowDetail.getStandard().isEmpty()) {
                    seqName += sopFlowDetail.getStandard();
                }
                if (sopFlowDetail.getGlue() != null && !sopFlowDetail.getGlue().isEmpty()) {
                    seqName += " " + sopFlowDetail.getGlue().replace("\n", " ");
                }
                if (sopFlowDetail.getCarLine() != null && !sopFlowDetail.getCarLine().isEmpty()) {
                    seqName += " " + sopFlowDetail.getCarLine().replace("\n", " ");
                }
                if (sopFlowDetail.getMargin() != null && !sopFlowDetail.getMargin().isEmpty()) {
                    seqName += " " + sopFlowDetail.getMargin().replace("\n", " ");
                }
                if (sopFlowDetail.getNeedleSpacing() != null && !sopFlowDetail.getNeedleSpacing().isEmpty()) {
                    seqName += " " + sopFlowDetail.getNeedleSpacing().replace("\n", " ");
                }
                if (sopFlowDetail.getSpacing() != null && !sopFlowDetail.getSpacing().isEmpty()) {
                    seqName += " " + sopFlowDetail.getSpacing().replace("\n", " ");
                }
                if (sopFlowDetail.getNeedle() != null && !sopFlowDetail.getNeedle().isEmpty()) {
                    seqName += " " + sopFlowDetail.getNeedle().replace("\n", " ");
                }
                if (seqName.isEmpty()) {
                    if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                        seqName = sopFlowDetail.getActions();
                    } else {
                        seqName = "新工序";
                    }
                }
            } else if ("5".equals(sopFlowDetail.getOperation()) || "6".equals(sopFlowDetail.getOperation()) || "8".equals(sopFlowDetail.getOperation())) {
                // 半成品、成型、皮底
                // 工序名称 = 动作 + 化学品 + 温度 + 压力 + 时间
                if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                    seqName += sopFlowDetail.getActions();
                }
                if (sopFlowDetail.getChemical() != null && !sopFlowDetail.getChemical().isEmpty()) {
                    seqName += " " + sopFlowDetail.getChemical().replace("\n", " ");
                }
                if (sopFlowDetail.getTemp() != null && !sopFlowDetail.getTemp().isEmpty()) {
                    seqName += " " + sopFlowDetail.getTemp().replace("\n", " ");
                }
                if (sopFlowDetail.getPressure() != null && !sopFlowDetail.getPressure().isEmpty()) {
                    seqName += " " + sopFlowDetail.getPressure().replace("\n", " ");
                }
                if (sopFlowDetail.getTime() != null && !sopFlowDetail.getTime().isEmpty()) {
                    seqName += " " + sopFlowDetail.getTime().replace("\n", " ");
                }
                if (seqName.isEmpty()) {
                    seqName = "新工序";
                }
                if (sopFlowDetail.getStandard() != null && !sopFlowDetail.getStandard().isEmpty()) {
                    sopFlowDetail.setRemark(sopFlowDetail.getStandard());
                }
            } else if ("1".equals(sopFlowDetail.getOperation())) {
                // 加工
                // 工序名称 = 动作 + 选项
                if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                    seqName += sopFlowDetail.getActions();
                }
                for (int i = 1; i <= 10; i++) {
                    String methodName = "getProcessOption" + i;
                    try {
                        String methodValue = (String) sopFlowDetail.getClass().getMethod(methodName).invoke(sopFlowDetail);
                        if (methodValue != null && !methodValue.isEmpty()) {
                            seqName += " " + methodValue.replace("\n", " ");
                        }
                    } catch (Exception e) {
                        log.error("SOP - 修改整体流程功能报错！{}", e.getMessage());
                    }
                }
                if (seqName.isEmpty()) {
                    seqName = "新工序";
                }
                if (sopFlowDetail.getStandard() != null && !sopFlowDetail.getStandard().isEmpty()) {
                    sopFlowDetail.setRemark(sopFlowDetail.getStandard());
                }
            } else {
                // 其它
                if (sopFlowDetail.getActions() != null && !sopFlowDetail.getActions().isEmpty()) {
                    seqName += sopFlowDetail.getActions();
                }
                if (seqName.isEmpty()) {
                    seqName = "新工序";
                }
            }
            sopFlowDetail.setSeqName(seqName);
            sopMapper.updateFlowDetail(sopFlowDetail);
            sopMapper.deleteStandardAndCheckPoint(sopFlowDetail);
            sopMapper.addFlowStandard(sopFlowDetail);
            sopMapper.addFlowCheckPoint(sopFlowDetail);
            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 修改整体流程功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int updateOverallPicture(SopFlowDetail sopFlowDetail) {
        int result = -1;
        try {
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH");
            String format = sdf.format(date);

            String imgPath = targetFolder + File.separator + "img" + File.separator + format;

            List<SopFlowPicture> imgList1 = sopFlowDetail.getImgList();
            List<SopFlowPicture> imgList2 = new ArrayList<>();

            sopMapper.deleteFlowPicture(sopFlowDetail);

            for (SopFlowPicture img : imgList1) {
                if (img.getId() < 0) {
                    if (!FileUtil.exist(imgPath)) {
                        FileUtil.mkdir(imgPath);
                    }

                    // Base64 字符串
                    String base64 = img.getImgUrl();
                    // 扩展名
                    String extension = base64.substring(base64.indexOf("/") + 1, base64.indexOf(";"));
                    // 主要数据
                    String data = base64.substring(base64.indexOf(",") + 1);
                    // 随机生成文件名
                    String fileName = IdUtil.randomUUID() + "." + extension;

                    byte[] bytes = Base64.getDecoder().decode(data);
                    Path path = Paths.get(imgPath + File.separator + fileName);
                    Files.write(path, bytes);

                    String fileDownloadUri = "/img/" + format + "/" + fileName;
                    img.setImgUrl(fileDownloadUri);
                    imgList2.add(img);
                } else {
                    imgList2.add(img);
                }
            }
            sopFlowDetail.setImgList(imgList2);
            for (SopFlowPicture img : imgList2) {
                sopMapper.addFlowPicture(sopFlowDetail.getModel(), sopFlowDetail.getOperation(), sopFlowDetail.getRtgCode(), sopFlowDetail.getSeqNo(), img.getImgUrl());
            }

            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 修改整体流程图片功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int addOverallFlow(SopProcessFlow sopProcessFlow, List<SopFlowAction> actionList) {
        int result = -1;
        try {
            for (SopFlowAction action : actionList) {
                sopProcessFlow.setActions(action.getActionCn());
                sopMapper.addOverallFlow(sopProcessFlow);
                if (action.getId() > 0) {
                    SopFlowDetail sopFlowDetail = new SopFlowDetail();
                    BeanUtils.copyProperties(sopProcessFlow, sopFlowDetail);
                    String seqNo = sopMapper.getMaxSeqNo(sopProcessFlow.getModel(), sopProcessFlow.getOperation(), sopProcessFlow.getRtgCode());
                    sopFlowDetail.setSeqNo(seqNo);
                    selectOverallAction(action.getId(), sopFlowDetail);
                }
            }
            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 添加整体流程 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public int updateCompleteState(String model, String operation, String rtgCode, String cplFlag) {
        int result = -1;
        try {
            sopMapper.updateCompleteState(model, operation, rtgCode, cplFlag);
            result = 1;
        } catch (Exception e) {
            log.error("SOP - 修改工序完成状态 功能报错！{}", e.getMessage());
            return result;
        }
        return result;
    }

    @Override
    public List<SopCreator> getCreatorList() {
        List<SopCreator> result = new ArrayList<>();
        try {
            result = sopMapper.getCreatorList();
        } catch (Exception e) {
            log.error("SOP - 获取创建人列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getOperationOption() {
        List<String> result = new ArrayList<>();
        try {
            result = sopMapper.getOperationOption();
        } catch (Exception e) {
            log.error("SOP - 获取制程选项 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopTagOption> getTagOption() {
        List<SopTagOption> result = new ArrayList<>();
        try {
            result = sopMapper.getTagOption();
        } catch (Exception e) {
            log.error("SOP - 获取标签选项 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getTranslationModelList(boolean isCompleted) {
        List<String> result = new ArrayList<>();
        try {
            System.out.println(isCompleted);
            result = sopMapper.getTranslationModelList(isCompleted);
        } catch (Exception e) {
            log.error("SOP - 获取翻译型体列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getTranslationOperationList(boolean isCompleted) {
        List<String> result = new ArrayList<>();
        try {
            result = sopMapper.getTranslationOperationList(isCompleted);
        } catch (Exception e) {
            log.error("SOP - 获取翻译制程列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getTranslationProSeqList(boolean isCompleted) {
        List<String> result = new ArrayList<>();
        try {
            result = sopMapper.getTranslationProSeqList(isCompleted);
        } catch (Exception e) {
            log.error("SOP - 获取翻译详细制程列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopTranslation> getTranslationList(String model, String operation, String proSeq, int pageNo, int pageSize, boolean isCompleted, boolean needTranslation) {
        List<SopTranslation> result = new ArrayList<>();
        try {
            result = sopMapper.getTranslationList(model, operation, proSeq, pageNo, pageSize, isCompleted, needTranslation);
        } catch (Exception e) {
            log.error("SOP - 获取工序翻译列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public int getTranslationCount() {
        int result = -1;
        try {
            result = sopMapper.getTranslationCount();
        } catch (Exception e) {
            log.error("SOP - 获取已完成待翻译数量 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int updateTranslation(SopTranslation sopTranslation) {
        int result = -1;
        try {
            int updateCount = sopMapper.updateTranslation(sopTranslation);
            if (updateCount == 0) {
                sopMapper.addTranslation(sopTranslation);
            }
            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 修改工序翻译 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopPress> getPressList(String model_no, String operation, String rtg_code, String component_name) {
        List<SopPress> result = new ArrayList<>();
        try {
            result = sopMapper.getPressList(model_no, operation, rtg_code, component_name);
        } catch (Exception e) {
            log.error("SOP - 获取热冷压规格列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int savePress(List<SopPress> pressList) {
        int result = -1;
        try {
            for (SopPress sopPress : pressList) {
                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH");
                String format = sdf.format(date);
                String imagePath = targetFolder + File.separator + "img" + File.separator + format;

                String images = sopPress.getProbe_location_image();
                List<String> imageList = new ArrayList<>();

                for (String image : images.split("\\|")) {
                    if (image.isEmpty()) {
                        continue;
                    }
                    if (image.startsWith("data:image")) {
                        String data = image.substring(image.indexOf(",") + 1);
                        String extension = image.substring(image.indexOf("/") + 1, image.indexOf(";"));
                        String fileName = IdUtil.randomUUID() + "." + extension;

                        byte[] bytes = Base64.getDecoder().decode(data);
                        Path path = Paths.get(imagePath + File.separator + fileName);
                        Files.createDirectories(path.getParent());
                        Files.write(path, bytes);

                        imageList.add("/img/" + format + "/" + fileName);
                    } else {
                        imageList.add(image);
                    }
                }

                sopPress.setProbe_location_image(String.join(",", imageList));

                int updateCount = sopMapper.updatePress(sopPress);
                if (updateCount == 0) {
                    sopMapper.addPress(sopPress);
                }
            }
            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 保存热冷压规格 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopSku> getSkuList(String model) {
        List<SopSku> result = new ArrayList<>();
        try {
            result = sopMapper.getSkuList(model);
        } catch (Exception e) {
            log.error("SOP - 获取 SKU 列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public int updateInfoSku(SopProcessInfo sopProcessInfo) {
        int result = -1;
        try {
            result = sopMapper.updateInfoSku(sopProcessInfo);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("SOP - 修改工序信息 SKU 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopExcelExport> getExcelExportList(String model, String operation, String rtgCode) {
        List<SopExcelExport> result = new ArrayList<>();
        try {
            result = sopMapper.getExcelExportList(model, operation, rtgCode, null);
            for (SopExcelExport excelExport : result) {
                SopProcessFlow sopProcessFlow = new SopProcessFlow();
                sopProcessFlow.setModel(excelExport.getModel());
                sopProcessFlow.setOperation(excelExport.getOperation());
                sopProcessFlow.setRtgCode(excelExport.getRtgCode());
                sopProcessFlow.setSeqNo(excelExport.getSeqNo());
                List<SopFlowPicture> imgList = sopMapper.getFlowPictureList(sopProcessFlow);
                if (imgList != null) {
                    excelExport.setImgList(imgList);
                }
            }
        } catch (Exception e) {
            log.error("SOP - 获取 Excel 导出列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<SopExcelExport> getExcelExportList(String model, String operation, String rtgCode, String partName) {
        List<SopExcelExport> result = new ArrayList<>();
        try {
            result = sopMapper.getExcelExportList(model, operation, rtgCode, partName);
            for (SopExcelExport excelExport : result) {
                SopProcessFlow sopProcessFlow = new SopProcessFlow();
                sopProcessFlow.setModel(excelExport.getModel());
                sopProcessFlow.setOperation(excelExport.getOperation());
                sopProcessFlow.setRtgCode(excelExport.getRtgCode());
                sopProcessFlow.setSeqNo(excelExport.getSeqNo());
                List<SopFlowPicture> imgList = sopMapper.getFlowPictureList(sopProcessFlow);
                if (imgList != null) {
                    excelExport.setImgList(imgList);
                }
            }
        } catch (Exception e) {
            log.error("SOP - 获取 Excel 导出列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getPartList() {
        List<String> result = new ArrayList<>();
        try {
            result = sopMapper.getPartList();
        } catch (Exception e) {
            log.error("SOP - 获取部位列表 功能报错！{}", e.getMessage());
        }
        return result;
    }

    @Override
    public List<String> getExistingPartList(String model, String operation, String rtgCode) {
        List<String> result = new ArrayList<>();
        try {
            result = sopMapper.getExistingPartList(model, operation, rtgCode);
        } catch (Exception e) {
            log.error("SOP - 获取已存在部位列表 功能报错！{}", e.getMessage());
        }
        return result;
    }
}
