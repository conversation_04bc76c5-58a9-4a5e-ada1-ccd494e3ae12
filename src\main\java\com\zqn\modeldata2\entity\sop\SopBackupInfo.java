package com.zqn.modeldata2.entity.sop;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 备份工序信息
 */
@Data
public class SopBackupInfo {
    // ID
    private long id;
    // 型体编号
    private String model_no;
    // 制程
    private String operation;
    // 主要代码
    private String rtg_code;
    // 生产类型
    private String rtg_type;
    // 材质
    private String material;
    // 版本
    private String revision;
    // 宽放率
    private int allow;
    // 整备时间(小时@批)
    private int setup_hr;
    // 计划时产能
    private int hr_cap;
    // 需求总产量
    private int req_qty;
    // 标准配置人员
    private int std_emp;
    // 建议配置人员
    private int pps_emp;
    // 建议时间(秒@双)
    private int pps_time;
    // 建议时产能
    private int pps_rate;
    // 备注
    private String remark;
    // 停用
    private String inv_flag;
    // 停用人
    private String inv_user;
    // 停用日期
    private Timestamp inv_date;
    // 建立人
    private String ins_user;
    // 建立日期
    private Timestamp ins_date;
    // 修改人
    private String upd_user;
    // 修改日期
    private Timestamp upd_date;
    // 线外操作总时间
    private int loo_time;
    // 准备总标准时间
    private int pts_time;
    // 预估时产能
    private int est_rate;
    // 型体版次
    private String model_ver;
    // 对应材质
    private String dmaterial;
    // 裁断真皮时产能
    private int cut_cap;
    // 龙门式裁断时产能
    private int cut_cap2;
    // 电脑裁断时产能
    private int cut_cap3;
    // 平台式裁断时产能
    private int cut_cap4;
    // 技术主管
    private String sign_name;
    // 审核
    private String chk_flag;
    // 审核人
    private String chk_user;
    // 审核日期
    private Timestamp chk_date;
    // 对应材质修正日期
    private Timestamp dmat_update;
    // 大类
    private String rtg_class;
    // 详细制程
    private String pro_seq;
    // 制程排头
    private String shoe_make_head;
    // 版师
    private String printmaker;
    // 高级技师
    private String senior_technician;
    // 完成状态
    private String cpl_flag;
    // 完成时间
    private Timestamp cpl_date;
    // 创建方式
    private String created_tag;
    // 成品编号
    private String pro_nos;
}
