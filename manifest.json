{
    "name" : "STELLA VNPCC",
    "appid" : "__UNI__C0FD491",
    "description" : "",
    "versionName" : "1.6.6",
    "versionCode" : 22,
    "app-plus" : {
        "compatible" : {
            //uni-app兼容模式
            "ignoreVersion" : true
        },
        /* 5+App特有相关 */
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "nvueStyleCompiler" : "uni-app",
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "orientation" : [ "landscape-primary", "landscape-secondary" ],
        "modules" : {
            "Barcode" : {},
            "Camera" : {},
            "VideoPlayer" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "arm64-v8a" ],
                "minSdkVersion" : 21
            },
            "ios" : {
                "dSYMs" : false,
                "idfa" : false
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "ad" : {},
                "speech" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common"
            }
        },
        "softinput" : {
            "navBar" : "auto", //可选，字符串类型，iOS平台软键盘上导航条的显示模式，可取值auto、none
            "auxiliary" : false, //可选，Boolean类型，是否开启辅助输入功能
            "mode" : "adjustResize" //可选，字符串类型，弹出系统软键盘模式，可取值adjustResize、adjustPan
        },
        "nativePlugins" : {
            "Mpaas-Scan" : {
                "AppId" : "ALIPUB52BEE15220842",
                "License" : "eMvNHvz7n5K1/gikhau+3UVOe7xnsKkbnz5CD+AEhkFQoT7zC8EJLbutmNJ39eHZe+pt9V1WGItZc962RLRxawAK3lt0dwB8XuOoULXIfwjsjcbtNNDCSflbhBWuXZkp7tvhTYs2IdD+hWd7RZubxhCyKtzNawxkfMLsOLSA37txJhingZZLhsJmXsjwGB2+WmkgTx3bmx7shN+daOejJC3nTIDvvY0Gq/r9VkS5gdjseIR0YgRrUmU7fQthThmZZV6L4xTuN7N6r4RC4zczL9QUJELXCumPnfrW0gFlXDQfc/ZsqQkWSqzNG4uyoqiwFplwPdS/nijRScz7VNqVjg==",
                "WorkspaceId" : "default",
                "__plugin_info__" : {
                    "name" : "支付宝原生扫码插件",
                    "description" : "支付宝原生扫码组件，包体积仅0.7MB，15分钟即可完成接入。同时，mPaaS提供「扫码分析」大盘",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=2636",
                    "android_package_name" : "uni.UNIC0FD491",
                    "ios_bundle_id" : "",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "2636",
                    "parameters" : {
                        "AppId" : {
                            "des" : "Android平台的AppId，请填写Android的config文件中的appId对应的值",
                            "key" : "mobilegw.appid",
                            "value" : ""
                        },
                        "License" : {
                            "des" : "Android平台的License,，请填写Android的config文件中的mpaasConfigLicense对应的值",
                            "key" : "mpaasConfigLicense",
                            "value" : ""
                        },
                        "WorkspaceId" : {
                            "des" : "Android平台的WorkspaceId，请填写Android的config文件中的workspaceId对应的值",
                            "key" : "workspaceId",
                            "value" : ""
                        }
                    }
                }
            }
        },
        "nvueLaunchMode" : ""
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "vueVersion" : "3",
    "locale" : "auto",
    "fallbackLocale" : "zh-Hans",
    "h5" : {
        "router" : {
            "base" : "/vnpcc"
        },
        "devServer" : {
            "https" : false
        }
    }
}
/* 5+App特有相关 */

