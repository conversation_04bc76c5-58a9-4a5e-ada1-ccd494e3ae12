package com.zqn.modeldata2.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.Qdpt;
import com.zqn.modeldata2.entity.QdptDt;
import com.zqn.modeldata2.mapper.QdptMapper;
import com.zqn.modeldata2.service.QdptService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
public class QdptServiceImpl implements QdptService {

    @Resource
    private QdptMapper qdptMapper;

    private static final Logger LOG = LoggerFactory.getLogger(QdptServiceImpl.class);


    @Override
    public R<PageInfo<Qdpt>> query(int pageNo, int pageSize, Date startTime, Date endTime, String brand, String devType, Integer fileType, Integer cutComplType,String sortType) {
        BigDecimal sumTotQty = qdptMapper.queryTotQtyCount(startTime, endTime, brand, devType, fileType, cutComplType);
        PageHelper.startPage(pageNo, pageSize);
        List<Qdpt> list = qdptMapper.query(startTime, endTime, brand, devType, fileType, cutComplType,sortType);
        if (list.size() > 0) {
            for (Qdpt qdpt : list) {
                qdpt.setTotal_order_quantity(sumTotQty);
            }
        }
        PageInfo<Qdpt> pageInfo = new PageInfo<>(list);
        return R.success(pageInfo);
    }

    @Override
    public List<JSONObject> queryAllDevType() {
        List<JSONObject> result = new ArrayList<JSONObject>();
        List<String> devTypes = qdptMapper.queryAllDevType();
           if (devTypes.size() > 0) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", "");
            jsonObject.put("text", "請選擇");
            result.add(jsonObject);
        }
        for (String devType : devTypes) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", devType);
            jsonObject.put("text", devType);
            result.add(jsonObject);
        }
        return result;
    }


    @Transactional
    @Override
    public Integer update(Qdpt qdpt) throws Exception {
        if (StrUtil.isEmpty(qdpt.getOrd_no()) || StrUtil.isEmpty(qdpt.getItem_no())) {
            throw new Exception("未选中需更新数据！");
        }
        return qdptMapper.update(qdpt.getOrd_no(), qdpt.getItem_no());
    }

    @Override
    @Transactional
    public Integer batchUpdate(List<Qdpt> qdpts) throws Exception {
        int i = 0;
        for (Qdpt qdpt : qdpts) {
            if (StrUtil.isEmpty(qdpt.getOrd_no()) || StrUtil.isEmpty(qdpt.getItem_no())) {
                throw new Exception("未选中需更新数据！");
            }
            i = i + qdptMapper.update(qdpt.getOrd_no(), qdpt.getItem_no());
        }
        return i;
    }

    @Override
    public BigDecimal findTouRuLogTotQtyCount(List<String> orderNos) {
       return qdptMapper.queryTouRuLogTotQtyCount(orderNos);

    }

    @Override
    @DS("app")
    public void insertLog(String ordNo, String itemNo) {
        qdptMapper.insertLog(ordNo, itemNo);
    }

    @Override
    @DS("app")
    public List<Qdpt> queryLog() {
        return qdptMapper.queryLog();
    }

    /**
     * 根据订单号查询仓库扫描明细。
     * <p>
     * 本方法通过调用qdptMapper的queryDetailTableData方法，使用订单号作为条件来查询详细的表格数据。
     * 具体的查询逻辑由qdptMapper负责实现，这里只是作为一个调用接口，将查询结果以List<Qdpt>的形式返回。
     *
     * @param ordNo 订单号，作为查询条件。
     * @return 返回查询到的详细表格数据列表，如果未查询到数据，则返回空列表。
     */
    @Override
    public List<QdptDt> queryDetailTableData(String ordNo) {
        return qdptMapper.queryDetailTableData(ordNo);
    }

    @Override
    @Transactional
    public String manualClose(Qdpt qdpt) {
        //因为样品单可能有多个出货期，而扫描是基于样品单号
        //所以这个扫描明细摆放结案状态有可能有多笔资料
        //先查询出所有需要结案的项次
        List<Qdpt> manualCloseList = qdptMapper.selectManualClose(qdpt.getOrd_no());

        String result = null;
        for (Qdpt item : manualCloseList) {
            Map<String, Object> params = new HashMap<>();
            params.put("ord_no", qdpt.getOrd_no());
            params.put("item_no", item.getItem_no());
            params.put("o_return", null);
            try {
                qdptMapper.manualClose(params);
                Object o_return = params.get("o_return");
                if (o_return != null) {
                    String transNo = o_return.toString();
                    result += transNo;
                } else {
                    result = "操作成功！";
                }
            } catch (Exception e) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                LOG.error("手动结案 功能报错！{}", e.getMessage());
                result = errorMsg("", e);
                throw new RuntimeException(result);
            }
        }
        return result;
    }

    private static String errorMsg(String errorMsg, Exception e) {
        // 改进异常处理逻辑，安全地获取异常的深层原因
        Throwable cause = e;
        while (cause != null && cause.getCause() != null) {
            cause = cause.getCause();
        }
        // 确保获取到最后一个cause，即使它是null
        String errorCauseMessage = (cause != null) ? cause.getMessage() : "Unknown Error";

        // 优化字符串处理，合并替换操作，减少不必要的中间变量赋值
        errorCauseMessage = errorCauseMessage.replaceAll("\n", ";")
                .replaceAll("\"", "")
                .replaceAll("/", "")
                .replaceAll("\r", "");

        // 增加边界条件检查，确保substring不会抛出异常
        int begin = errorCauseMessage.indexOf(";");
        if (begin >= 0) {
            errorCauseMessage = errorCauseMessage.substring(0, begin);
        }

        // 检查处理后的错误信息长度，确保其不会因为过度处理而丢失信息
        if (errorCauseMessage.length() > 1) {
            errorCauseMessage = errorCauseMessage.replace("/", "").replace("\r", "");
        }

        return errorCauseMessage;
    }
}
