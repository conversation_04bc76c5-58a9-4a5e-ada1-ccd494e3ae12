<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.ThirdMapper">
    <resultMap id="ResultMap" type="java.util.HashMap">
        <result property="model_no" column="MODEL_NO" jdbcType="VARCHAR"/>
        <result property="procs_type" column="PROCS_TYPE" jdbcType="INTEGER"/>
        <result property="part_seq" column="PART_SEQ" jdbcType="VARCHAR"/>
        <result property="part_name" column="PART_NAME" jdbcType="VARCHAR"/>
        <result property="part_spec" column="PART_SPEC" jdbcType="INTEGER"/>
        <result property="add_per" column="ADD_PER" jdbcType="DOUBLE"/>
        <result property="ins_user" column="INS_USER" jdbcType="VARCHAR"/>
        <result property="ins_date" column="INS_DATE" jdbcType="DATE"/>
        <result property="upd_user" column="UPD_USER" jdbcType="VARCHAR"/>
        <result property="upd_date" column="UPD_DATE" jdbcType="DATE"/>
        <result property="column_order" column="COLUMN_ORDER" jdbcType="VARCHAR"/>
        <result property="column_seq" column="COLUMN_SEQ" jdbcType="VARCHAR"/>
        <result property="column_no" column="COLUMN_NO" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getOption1" resultMap="ResultMap">
        select rn as column_order, column_seq, column_no
        from (select rownum rn, t.*
              from (select column_seq, column_no, column_desc
                    from sy_xcodeval
                    where code_no = 'BC0161'
                      and instr(column_desc, '1') > 0
                    order by column_seq) t)
    </select>

    <select id="getOption2" resultMap="ResultMap">
        select rn as column_order, column_seq, column_no
        from (select rownum rn, t.*
              from (select column_seq, column_no, column_desc
                    from sy_xcodeval
                    where code_no = 'BC0161'
                      and instr(column_desc, '2') > 0
                    order by column_seq) t)
    </select>

    <select id="getPart1" resultType="com.zqn.modeldata2.entity.CkSmodelpa">
        select model_no,   -- 型体
               procs_type, -- 规格类型
               part_seq,   -- 部位序号
               part_name,  -- 部位名称
               part_spec,  -- 部位规格
               add_per,    -- 级放数
               ins_user,   -- 添加者
               ins_date,   -- 添加时间
               upd_user,   -- 修改者
               upd_date    -- 修改时间
        from ck_smodelpa@dserp
        where model_no = #{ckSmodelpa.model_no}
          and procs_type = '1'
        order by part_seq
    </select>

    <select id="getPart2" resultType="com.zqn.modeldata2.entity.CkSmodelpa">
        select model_no,   -- 型体
               procs_type, -- 规格类型
               part_seq,   -- 部位序号
               part_name,  -- 部位名称
               part_spec,  -- 部位规格
               add_per,    -- 级放数
               ins_user,   -- 添加者
               ins_date,   -- 添加时间
               upd_user,   -- 修改者
               upd_date    -- 修改时间
        from ck_smodelpa@dserp
        where model_no = #{ckSmodelpa.model_no}
          and procs_type = '2'
        order by part_seq
    </select>

    <insert id="addTemporaryPart">
        insert into ck_smodelpa@dserp (model_no, -- 型体
                                 procs_type, -- 规格类型
                                 part_seq, -- 部位序号
                                 part_name, -- 部位名称
                                 part_spec, -- 部位规格
                                 add_per, -- 级放数
                                 ins_user, -- 添加者
                                 ins_date, -- 添加时间
                                 upd_user, -- 修改者
                                 upd_date -- 修改时间
        )
        values (#{ckSmodelpa.model_no},
                #{ckSmodelpa.procs_type},
                #{ckSmodelpa.part_seq},
                #{ckSmodelpa.part_name},
                #{ckSmodelpa.part_spec},
                #{ckSmodelpa.add_per},
                #{ckSmodelpa.ins_user},
                sysdate,
                #{ckSmodelpa.upd_user},
                sysdate)
    </insert>

    <insert id="addAllPart">
        insert all
        <foreach collection="ckSmodelpaList" item="ckSmodelpa" separator=" ">
            into ck_smodelpa@dserp (
            model_no, -- 型体
            procs_type, -- 规格类型
            part_seq, -- 部位序号
            part_name, -- 部位名称
            part_spec, -- 部位规格
            add_per, -- 级放数
            ins_user, -- 添加者
            ins_date, -- 添加时间
            upd_user, -- 修改者
            upd_date -- 修改时间
            ) values (
            #{ckSmodelpa.model_no},
            #{ckSmodelpa.procs_type},
            #{ckSmodelpa.part_seq},
            #{ckSmodelpa.part_name},
            #{ckSmodelpa.part_spec},
            #{ckSmodelpa.add_per},
            #{ckSmodelpa.ins_user},
            sysdate,
            #{ckSmodelpa.upd_user},
            sysdate
            )
        </foreach>
        select * from dual
    </insert>

    <update id="updatePart">
        update ck_smodelpa@dserp
        set part_spec = #{ckSmodelpa.part_spec}, -- 部位规格
            add_per   = #{ckSmodelpa.add_per},   -- 级放数
            upd_user  = #{ckSmodelpa.upd_user},  -- 修改者
            upd_date  = sysdate                  -- 修改时间
        where model_no = #{ckSmodelpa.model_no}     -- 型体
          and procs_type = #{ckSmodelpa.procs_type} -- 规格类型
          and part_name = #{ckSmodelpa.part_name} -- 部位名称
    </update>

    <delete id="deletePart">
        delete
        from ck_smodelpa@dserp
        where model_no = #{ckSmodelpa.model_no}     -- 型体
          and procs_type = #{ckSmodelpa.procs_type} -- 规格类型
          and part_name = #{ckSmodelpa.part_name} -- 部位名称
    </delete>

    <delete id="batchDeletePart">
        delete from ck_smodelpa@dserp
        where
        <foreach collection="ckSmodelpaList" item="ckSmodelpa" separator="or">
            (model_no = #{ckSmodelpa.model_no} -- 型体
            and procs_type = #{ckSmodelpa.procs_type} -- 规格类型
            and part_name = #{ckSmodelpa.part_name}) -- 部位名称
        </foreach>
    </delete>
</mapper>