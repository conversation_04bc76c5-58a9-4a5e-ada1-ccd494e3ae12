package com.zqn.modeldata2.entity.sop;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * Excel 导出
 */
@Data
public class SopExcelExport {
    // 型体
    private String model;
    // 制程
    private String operation;
    // 主要代码
    private String rtgCode;
    // 工序编号
    private String seqNo;
    // 排序号
    private int skey;
    // 工序名称
    private String seqName;
    // 部位
    private String partName;
    // 动作
    private String actions;
    // 操作标准
    private String standard;
    // 自检点
    private String checkPoint;
    // 图片列表
    private List<SopFlowPicture> imgList;
    // 工具
    private String tools;
    // 机器
    private String machine;
    // 边距
    private String margin;
    // 温度
    private String temp;
    // 压力
    private String pressure;
    // 胶水
    private String glue;
    // 车线
    private String carLine;
    // 化学品
    private String chemical;
    // 针距
    private String needleSpacing;
    // 间距
    private String spacing;
    // 车针
    private String needle;
    // 时间
    private String time;
    // 防护用品
    private String defence;
    private String processOption1;
    private String processOption2;
    private String processOption3;
    private String processOption4;
    private String processOption5;
    private String processOption6;
    private String processOption7;
    private String processOption8;
    private String processOption9;
    private String processOption10;
    // 型体描述
    private String modelDesc;
    // 楦头
    private String last;
    // Outsole
    private String osNo;
    // 制表人
    private String tab;
    // 创建人
    private String insUser;
    // 创建时间
    private Timestamp insDate;
    // 修改人
    private String updUser;
    // 修改时间
    private Timestamp updDate;
}
