<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.ScanMapper">
    <resultMap id="ResultMap" type="java.util.HashMap">
        <result property="count" column="COUNT" jdbcType="INTEGER"/>
        <result property="rn" column="RN" jdbcType="INTEGER"/>
        <result property="ord_no" column="ORD_NO" jdbcType="VARCHAR"/>
        <result property="model_no" column="MODEL_NO" jdbcType="VARCHAR"/>
        <result property="brand_no" column="BRAND_NO" jdbcType="VARCHAR"/>
        <result property="phase" column="PHASE" jdbcType="VARCHAR"/>
        <result property="pb_dept" column="PB_DEPT" jdbcType="INTEGER"/>
        <result property="pb_desc" column="PB_DESC" jdbcType="VARCHAR"/>
        <result property="pb_desc1" column="PB_DESC1" jdbcType="VARCHAR"/>
        <result property="pb_addr" column="PB_ADDR" jdbcType="VARCHAR"/>
        <result property="pb_qty" column="PB_QTY" jdbcType="INTEGER"/>
        <result property="dev_type" column="DEV_TYPE" jdbcType="VARCHAR"/>
        <result property="methoed" column="METHOED" jdbcType="VARCHAR"/>
        <result property="cl_flag" column="CL_FLAG" jdbcType="VARCHAR"/>
        <result property="cl_date" column="CL_DATE" jdbcType="DATE"/>
        <result property="wait_time" column="WAIT_TIME" jdbcType="DOUBLE"/>
        <result property="ins_date" column="INS_DATE" jdbcType="DATE"/>
        <result property="ins_user" column="INS_USER" jdbcType="VARCHAR"/>
        <result property="upd_date" column="UPD_DATE" jdbcType="DATE"/>
        <result property="upd_user" column="UPD_USER" jdbcType="VARCHAR"/>
        <result property="last_seq" column="LAST_SEQ" jdbcType="VARCHAR"/>
        <result property="last_no" column="LAST_NO" jdbcType="VARCHAR"/>
        <result property="ord_qty" column="ORD_QTY" jdbcType="INTEGER"/>
        <result property="column_seq" column="COLUMN_SEQ" jdbcType="VARCHAR"/>
        <result property="column_no" column="COLUMN_NO" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getCount" resultMap="ResultMap">
        select count(*) as count
        from mk_slastbar a, gc_sorder b
        where a.ord_no = b.ord_no
        and a.pb_addr in (
        select regexp_substr((
        select decode(email_addr,'A','A1,A2,A3','B','A4,A5,A6','C','A7,A8,A9') email_addr
        from sy_user t
        where user_id in (#{user_id})
        ), '[^,]+', 1, level)
        from dual connect by regexp_substr((
        select decode(email_addr,'A','A1,A2,A3','B','A4,A5,A6','C','A7,A8,A9') email_addr
        from sy_user t
        where user_id in (#{user_id})
        ), '[^,]+', 1, level) is not null
        )
        <if test="ins_user != null and ins_user.length() > 0">
            and a.ins_user = #{ins_user}
        </if>
        <if test="cl_flag != null and cl_flag.length() > 0">
            and a.cl_flag = #{cl_flag}
        </if>
        <if test="today == true">
            and to_char(a.ins_date, 'yyyy-mm-dd') = to_char(sysdate, 'yyyy-mm-dd')
        </if>
    </select>

    <select id="getScanException" resultType="com.zqn.modeldata2.entity.MkSlastbarPlus">
        select *
        from (
        select rownum rn, t.* from(
        select a.ord_no, -- 样品单号
        b.model_no, -- 型体编号
        b.brand_no, -- 品牌
        b.phase, -- 阶段
        a.pb_dept, -- 责任部门
        a.pb_desc, -- 异常说明
        a.pb_desc1, -- 问题说明1
        a.pb_addr, -- 异常地点
        a.pb_qty, -- 问题数量
        b.dev_type, -- 样品类型
        a.methoed, -- 解决方法
        a.cl_flag, -- 结案状态
        a.cl_date, -- 结案日期
        round(
        case when a.cl_flag ='Y'
        then datediff('HH',a.ins_date, a.cl_date)
        else datediff('HH', a.ins_date, sysdate) end, 2) wait_time, --等待時間
        a.ins_date,
        a.ins_user,
        a.upd_date,
        a.upd_user
        from mk_slastbar a, gc_sorder b
        where a.ord_no = b.ord_no
        <if test="ins_user != null and ins_user.length() > 0">
            and a.ins_user = #{ins_user}
        </if>
        <if test="cl_flag != null and cl_flag.length() > 0">
            and a.cl_flag = #{cl_flag}
        </if>
        <if test="today == true">
            and to_char(a.ins_date, 'yyyy-mm-dd') = to_char(sysdate, 'yyyy-mm-dd')
        </if>
        order by a.ins_date desc
        ) t
        )
        where rn &gt; (#{page_no} - 1) * #{page_size}
        and rn &lt;= #{page_no} * #{page_size}
    </select>

    <select id="getDept" resultType="com.zqn.modeldata2.entity.ColumnMap">
        select column_seq, column_no
        from sy_xcodeval
        where code_no = 'NC025'
    </select>

    <select id="getDesc" resultType="com.zqn.modeldata2.entity.ColumnMap">
        select column_seq, column_no
        from sy_xcodeval
        where code_no = 'NC026-1'
    </select>

    <select id="getAddr" resultType="com.zqn.modeldata2.entity.ColumnMap">
        select column_seq, column_no
        from sy_xcodeval
        where code_no = 'NC027-1'
    </select>

    <select id="getBarCodeA" resultMap="ResultMap">
        select upper(a.ord_no) as ord_no,
               b.last_seq,
               b.last_no,
               b.brand_no,
               a.ord_qty
        from gc_sorder a,
             ck_smodel x,
             be_moudel y,
             bf_last b
        where a.model_no = x.model_no
          and x.module_no = y.module_no
          and y.last_seq = b.last_seq
          and upper(a.ord_no) = upper(#{ord_no})
    </select>

    <select id="getBarCodeB" resultMap="ResultMap">
        select column_seq, column_no
        from sy_xcodeval
        where code_no = 'NC025'
          and column_seq = substr(#{column_seq}, 4, 1)
    </select>

    <select id="getBarCodeC" resultMap="ResultMap">
        select column_seq, column_no
        from sy_xcodeval
        where code_no = 'NC026-1'
          and column_seq = substr(#{column_seq}, 4, 2)
    </select>

    <select id="getBarCodeD" resultMap="ResultMap">
        select column_seq, column_no
        from sy_xcodeval
        where code_no = 'NC027-1'
          and column_seq = substr(#{column_seq}, 4, 2)
    </select>

    <select id="getOption" resultType="com.zqn.modeldata2.entity.ColumnMap">
        select column_seq, column_no
        from sy_xcodeval
        where code_no = 'NC026-2'
          and column_desc = #{mkSlastbar.pb_desc}
          and column_eng = case (select max(column_desc)
                                 from sy_xcodeval
                                 where code_no = 'NC027-1'
                                   and column_seq = #{mkSlastbar.pb_addr})
                               when '面部' then 'U'
                               when '半成品' then 'B'
                               else null
            end
        order by column_seq
    </select>

    <insert id="addException">
        insert into mk_slastbar(ord_no,
                                pb_qty,
                                pb_desc1,
                                pb_dept,
                                pb_desc,
                                pb_addr,
                                cl_flag,
                                ins_date,
                                ins_user)
        values (#{mkSlastbar.ord_no},
                #{mkSlastbar.pb_qty},
                #{mkSlastbar.pb_desc1},
                #{mkSlastbar.pb_dept},
                #{mkSlastbar.pb_desc},
                #{mkSlastbar.pb_addr},
                'N',
                sysdate,
                #{mkSlastbar.ins_user})
    </insert>

    <update id="updateException">
        update mk_slastbar set
        methoed = #{mkSlastbar.methoed},
        upd_user = #{mkSlastbar.upd_user},
        upd_date = sysdate,
        cl_flag = #{mkSlastbar.cl_flag},
        <if test="mkSlastbar.cl_flag == 'Y'.toString()">
            cl_date = (case when cl_date is null then sysdate else cl_date end)
        </if>
        <if test="mkSlastbar.cl_flag == 'N'.toString()">
            cl_date = null
        </if>
        where ord_no = #{mkSlastbar.ord_no}
        and pb_dept = #{mkSlastbar.pb_dept}
        and pb_addr = #{mkSlastbar.pb_addr}
        and to_char(ins_date, 'yyyy-mm-dd hh24:mi:ss') = to_char(#{mkSlastbar.ins_date}, 'yyyy-mm-dd hh24:mi:ss')
    </update>

    <update id="updateMethod">
        update mk_slastbar
        set methoed  = #{mkSlastbar.methoed},
            upd_user = #{mkSlastbar.upd_user},
            upd_date = sysdate
        where ord_no = #{mkSlastbar.ord_no}
          and pb_dept = #{mkSlastbar.pb_dept}
          and pb_addr = #{mkSlastbar.pb_addr}
          and to_char(ins_date, 'yyyy-mm-dd hh24:mi:ss') = to_char(#{mkSlastbar.ins_date}, 'yyyy-mm-dd hh24:mi:ss')
    </update>

    <update id="updateState">
        update mk_slastbar set
        upd_user = #{mkSlastbar.upd_user},
        upd_date = sysdate,
        cl_flag = #{mkSlastbar.cl_flag},
        <if test="mkSlastbar.cl_flag == 'Y'.toString()">
            cl_date = (case when cl_date is null then sysdate else cl_date end)
        </if>
        <if test="mkSlastbar.cl_flag == 'N'.toString()">
            cl_date = null
        </if>
        where ord_no = #{mkSlastbar.ord_no}
        and pb_dept = #{mkSlastbar.pb_dept}
        and pb_addr = #{mkSlastbar.pb_addr}
        and to_char(ins_date, 'yyyy-mm-dd hh24:mi:ss') = to_char(#{mkSlastbar.ins_date}, 'yyyy-mm-dd hh24:mi:ss')
    </update>

    <delete id="deleteException">
        delete
        from mk_slastbar
        where ord_no = #{mkSlastbar.ord_no}
          and pb_dept = #{mkSlastbar.pb_dept}
          and pb_addr = #{mkSlastbar.pb_addr}
          and to_char(ins_date, 'yyyy-mm-dd hh24:mi:ss') = to_char(#{mkSlastbar.ins_date}, 'yyyy-mm-dd hh24:mi:ss')
    </delete>
</mapper>