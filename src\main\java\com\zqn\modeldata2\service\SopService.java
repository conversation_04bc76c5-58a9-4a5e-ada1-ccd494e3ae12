package com.zqn.modeldata2.service;

import com.zqn.modeldata2.entity.sop.*;

import java.util.List;
import java.util.Map;

/**
 * SOP 服务层接口
 */
public interface SopService {
    List<SopOperation> getOperationList();

    List<SopBrand> getBrandList();

    List<SopModel> getModelList(String brand);

    List<String> getAllModelList();

    SopPicture getModelPicture(String model);

    List<String> getInfoTypeList(String operation);

    List<SopMaterial> getInfoMaterialList(String model, String operation);

    List<String> getLastNosList(String brand);

    List<String> getOsNoList(String brand);

    List<SopProcessInfo> getProcessInfoList(SopProcessInfo sopProcessInfo);

    int updateInfoType(SopProcessInfo sopProcessInfo);

    int updateInfoMaterial(SopProcessInfo sopProcessInfo);

    int addProcessInfo(SopProcessInfo sopProcessInfo);

    int deleteProcessInfo(List<SopProcessInfo> processInfoList);

    List<SopProcessFlow> getProcessFlowList(SopProcessFlow sopProcessFlow);

    int addProcessFlow(SopProcessFlow sopProcessFlow);

    int insertProcessFlow(SopProcessFlow sopProcessFlow);

    List<SopTemplate> getTemplateList(String operation);

    int templateImportFlow(SopProcessFlow sopProcessFlow);

    int updateFlowSection(SopProcessFlow sopProcessFlow);

    int sortSection(SopProcessFlow sopProcessFlow);

    int updateFlowSequence(SopProcessFlow sopProcessFlow);

    SopFlowDetail getFlowDetail(SopProcessFlow sopProcessFlow);

    int saveFlowDetail(SopFlowDetail sopFlowDetail);

    int deleteProcessFlow(List<SopProcessFlow> processFlowList);

    int resetProcessFlow(SopProcessInfo sopProcessInfo);

    List<String> getFlowOptionList(Integer type, Integer dept);

    List<SopFlowAction> getFlowActionList(String proSeq);

    Map<String, String> selectFlowAction(long id);

    List<SopPreview> getPreviewList(String model, String operation, String rtgCode);

    int copyProcess(SopProcessInfo sopProcessInfo, boolean isCopyPicture, List<String> targetModelList);

    List<SopFlowDetail> getOverallFlow(String model, String operation, String rtgCode);

    int selectOverallAction(long id, SopFlowDetail sopFlowDetail);

    int updateOverallFlow(SopFlowDetail sopFlowDetail);

    int updateOverallPicture(SopFlowDetail sopFlowDetail);

    int addOverallFlow(SopProcessFlow sopProcessFlow, List<SopFlowAction> actionList);

    int updateCompleteState(String model, String operation, String rtgCode, String cplFlag);

    List<SopCreator> getCreatorList();

    List<String> getOperationOption();

    List<SopTagOption> getTagOption();

    List<String> getTranslationModelList(boolean isCompleted);

    List<String> getTranslationOperationList(boolean isCompleted);

    List<String> getTranslationProSeqList(boolean isCompleted);

    List<SopTranslation> getTranslationList(String model, String operation, String proSeq, int pageNo, int pageSize, boolean isCompleted, boolean needTranslation);

    int getTranslationCount();

    int updateTranslation(SopTranslation sopTranslation);

    List<SopPress> getPressList(String model_no, String operation, String rtg_code, String component_name);

    int savePress(List<SopPress> pressList);

    List<SopSku> getSkuList(String model);

    int updateInfoSku(SopProcessInfo sopProcessInfo);

    List<SopExcelExport> getExcelExportList(String model, String operation, String rtgCode);

    List<SopExcelExport> getExcelExportList(String model, String operation, String rtgCode, String partName);

    List<String> getPartList();

    List<String> getExistingPartList(String model, String operation, String rtgCode);
}
