package com.zqn.modeldata2.controller.export;

import cn.afterturn.easypoi.cache.ExcelCache;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.zqn.modeldata2.entity.sop.SopExcelExport;
import com.zqn.modeldata2.entity.sop.SopFlowPicture;
import com.zqn.modeldata2.service.SopService;
import com.zqn.sop.util.ExcelExportUtil;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

@Controller
@RequestMapping("/sop/ua/dxexport")
public class SopUADxExportController {

    @Autowired
    private SopService sopService;

    @Value("${uploadUrl}")
    private String TARGET_FOLDER;

    @GetMapping("/excel")
    public void exportOperationProcess(
            @RequestParam(value = "model") String model,
            @RequestParam(value = "operation") String operation,
            @RequestParam(value = "rtgCode") String rtgCode,
            HttpServletResponse response
    ) throws IOException {
        String filePath = "static/excel/ua/加工/电绣.xlsx";
        Workbook tempWorkbook = ExcelCache.getWorkbook(filePath, new Integer[]{0}, false);
        Map<Integer, List<Map<String, Object>>> resultMap = new TreeMap<>();
        List<String> sheetNameList = new ArrayList<>();

        int page = 0;
        int pageSize = 8;

        List<String> partList = sopService.getExistingPartList(model, operation, rtgCode);

        for (int partIndex = 0; partIndex < partList.size(); partIndex++) {
            List<SopExcelExport> exportList = sopService.getExcelExportList(model, operation, rtgCode, partList.get(partIndex));

            List<Map<String, Object>> mapList = new ArrayList<>();

            int deleteCount = (exportList.size() + pageSize - 1) / pageSize;
            StringBuilder topThread = new StringBuilder();
            StringBuilder bottomThread = new StringBuilder();
            StringBuilder needleSize = new StringBuilder();
            StringBuilder machineType = new StringBuilder();
            StringBuilder embroideryType = new StringBuilder();
            StringBuilder ofStitches = new StringBuilder();
            StringBuilder groupSize = new StringBuilder();
            StringBuilder standard = new StringBuilder();

            for (int sheetIndex = exportList.size() - 1; sheetIndex >= 0; sheetIndex--) {
                SopExcelExport excelExport = exportList.get(sheetIndex);

                if (excelExport.getProcessOption1() != null) {
                    topThread.insert(0, excelExport.getProcessOption1().trim());
                    if (sheetIndex != 0) {
                        topThread.insert(0, ",");
                    }
                }
                if (excelExport.getProcessOption2() != null) {
                    bottomThread.insert(0, excelExport.getProcessOption2().trim());
                    if (sheetIndex != 0) {
                        bottomThread.insert(0, ",");
                    }
                }
                if (excelExport.getProcessOption3() != null) {
                    needleSize.insert(0, excelExport.getProcessOption3().trim());
                    if (sheetIndex != 0) {
                        needleSize.insert(0, ",");
                    }
                }
                if (excelExport.getProcessOption4() != null) {
                    machineType.insert(0, excelExport.getProcessOption4().trim());
                    if (sheetIndex != 0) {
                        machineType.insert(0, ",");
                    }
                }
                if (excelExport.getProcessOption5() != null) {
                    embroideryType.insert(0, excelExport.getProcessOption5().trim());
                    if (sheetIndex != 0) {
                        embroideryType.insert(0, ",");
                    }
                }
                if (excelExport.getProcessOption6() != null) {
                    ofStitches.insert(0, excelExport.getProcessOption6().trim());
                    if (sheetIndex != 0) {
                        ofStitches.insert(0, ",");
                    }
                }
                if (excelExport.getProcessOption7() != null) {
                    groupSize.insert(0, excelExport.getProcessOption7().trim());
                    if (sheetIndex != 0) {
                        groupSize.insert(0, ",");
                    }
                }
                if (excelExport.getStandard() != null) {
                    standard.insert(0, excelExport.getStandard().trim());
                    standard.insert(0, "-");
                    standard.insert(0, excelExport.getSkey());
                    if (sheetIndex != 0) {
                        standard.insert(0, "\n");
                    }
                }

                Map<String, Object> map = new TreeMap<>();
                map.put("modelDesc", excelExport.getModelDesc());
                map.put("model", excelExport.getModel());
                map.put("item", page + excelExport.getSkey());
                map.put("componentName", partList.get(partIndex));
                map.put("topThread", topThread);
                map.put("bottomThread", bottomThread);
                map.put("needleSize", needleSize);
                map.put("machineType", machineType);
                map.put("embroideryType", embroideryType);
                map.put("ofStitches", ofStitches);
                map.put("groupSize", groupSize);
                map.put("standard", standard);
                map.put("empty", "");

                List<Map<String, Object>> list = new ArrayList<>();
                if (sheetIndex < deleteCount) {
                    for (int i = 0; i < exportList.size(); i++) {
                        if (i >= sheetIndex * pageSize && i <= ((sheetIndex + 1) * pageSize) - 1) {
                            SopExcelExport excelExport1 = exportList.get(i);
                            HashMap<String, Object> hashMap = new HashMap<>();
                            hashMap.put("item", i + 1);
                            hashMap.put("actions", excelExport1.getActions());
                            hashMap.put("empty", "");
                            list.add(hashMap);
                        }
                    }
                }
                map.put("list", list);
                if (mapList.size() < deleteCount && list.size() > 0) {
                    mapList.add(map);
                }
            }
            resultMap.put(partIndex, mapList);
            for (int i = 0; i < deleteCount; i++) {
                if (partList.get(partIndex) != null) {
                    sheetNameList.add(partList.get(partIndex) + " " + (i + 1));
                } else {
                    sheetNameList.add("未知部位 " + (i + 1));
                }
            }
            page += (exportList.size() / pageSize);
        }

        TemplateExportParams params = new TemplateExportParams(filePath, true);
        params.setTemplateWb(tempWorkbook);
        XSSFWorkbook workbook = (XSSFWorkbook) ExcelExportUtil.exportExcelClone(resultMap, params);

        for (int i = 0; i < sheetNameList.size(); i++) {
            settingPrintSetup(workbook.getSheetAt(i), (short) 80);
            workbook.setSheetName(i, sheetNameList.get(i));
        }

        for (int partIndex = 0; partIndex < partList.size(); partIndex++) {
            List<SopExcelExport> exportList = sopService.getExcelExportList(model, operation, rtgCode, partList.get(partIndex));
            int deleteCount = (exportList.size() + pageSize - 1) / pageSize;

            for (int pageIndex = 0; pageIndex < deleteCount; pageIndex++) {
                for (int imageIndex = pageSize * pageIndex; imageIndex < Math.min(pageSize * (pageIndex + 1), exportList.size()); imageIndex++) {
                    SopExcelExport excelExport = exportList.get(imageIndex);
                    List<SopFlowPicture> assetsPicture = excelExport.getImgList();

                    if (!CollectionUtils.isEmpty(assetsPicture)) {
                        List<byte[]> imageList = new ArrayList<>();

                        for (SopFlowPicture sopFlowPicture : assetsPicture) {
                            String picturePath = TARGET_FOLDER + sopFlowPicture.getImgUrl();
                            byte[] compressedImage = compressImage(picturePath);
                            imageList.add(compressedImage);
                        }

                        XSSFSheet sheet;
                        if (partList.get(partIndex) != null) {
                            sheet = workbook.getSheet(partList.get(partIndex) + " " + (pageIndex + 1));
                        } else {
                            sheet = workbook.getSheet("未知部位 " + (pageIndex + 1));
                        }
                        XSSFDrawing patriarch = sheet.createDrawingPatriarch();
                        insertImages(patriarch, workbook, imageList, 2 * (imageIndex - pageSize * pageIndex) + 8, 2, 2, 3);
                    }
                }
            }
        }

        String fileName = model + "-电绣-" + rtgCode + ".xlsx";

        response.reset();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*"); // 或者指定具体的域名
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        try {
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 压缩图片
    public static byte[] compressImage(String inputImagePath) {
        try {
            // 读取图像
            BufferedImage bufferImg = ImageIO.read(new File(inputImagePath));

            // 获取图片格式
            String formatName = inputImagePath.substring(inputImagePath.lastIndexOf(".") + 1);
            if (formatName.equals("jfif")) {
                formatName = "jpeg";
            }

            // 获取图片写入器
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(formatName);
            if (!writers.hasNext()) throw new IllegalStateException("No writers found");
            ImageWriter writer = writers.next();

            // 设置输出流
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            ImageOutputStream ios = ImageIO.createImageOutputStream(byteArrayOut);
            writer.setOutput(ios);

            // 设置压缩参数
            ImageWriteParam param = writer.getDefaultWriteParam();
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.2f); // 这里设置压缩质量
            }

            // 写入图像
            writer.write(null, new IIOImage(bufferImg, null, null), param);

            // 关闭流
            ios.close();
            writer.dispose();

            return byteArrayOut.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    // 插入图片
    private void insertImages(XSSFDrawing patriarch, XSSFWorkbook workbook, List<byte[]> imageBytesList, int startRow, int startCol, int rowHeight, int colWidth) {
        for (int i = 0; i < imageBytesList.size(); i++) {
            int col1 = startCol + i * colWidth + (i + 1);
            int row1 = startRow;
            int col2 = col1 + colWidth;
            int row2 = row1 + rowHeight;
            byte[] imageBytes = imageBytesList.get(i);
            XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, col1, row1, col2, row2);
            patriarch.createPicture(anchor, workbook.addPicture(imageBytes, XSSFWorkbook.PICTURE_TYPE_PNG));
        }
    }

    private void settingPrintSetup(Sheet targetSheet, short scale) {
        PrintSetup targetPrintSetup = targetSheet.getPrintSetup();
        targetPrintSetup.setPaperSize((short) 9);
        targetPrintSetup.setScale(scale);
        targetPrintSetup.setLandscape(true);
    }
}
